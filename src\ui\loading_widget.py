"""
Loading widget per caricamento stagione e giocatori
Simile a Football Manager con progressione dettagliata
"""
import sys
import time
from typing import Callable, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QProgressBar, QPushButton, QTextEdit, QApplication
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt6.QtGui import QFont, QPixmap
from pathlib import Path

from ..core.config import *
from ..core.utils import logger
from ..data.data_loader import data_loader
from ..competitions.season import Season
from ..competitions.competition import setup_italian_competitions
from ..competitions.calendar import generate_italian_league_calendar
from ..players.player_generator import player_generator

class SeasonLoadingWorker(QThread):
    """Worker thread per caricamento stagione in background"""

    # Segnali per comunicare progresso
    progress_updated = pyqtSignal(int, str)  # percentuale, messaggio
    task_completed = pyqtSignal(str)  # nome task
    loading_completed = pyqtSignal(dict)  # dati caricati
    loading_failed = pyqtSignal(str)  # messaggio errore

    def __init__(self):
        super().__init__()
        self.season = None
        self.competition_manager = None
        self.calendar = None
        self.teams_data = {}

    def run(self):
        """Esegue il caricamento completo"""
        try:
            # Task 1: Caricamento dati base (10%)
            self.progress_updated.emit(5, "Inizializzazione dati...")
            time.sleep(0.5)

            if not data_loader.is_loaded():
                self.progress_updated.emit(10, "Caricamento nomi e campionati...")
                if not data_loader.load_all_data():
                    self.loading_failed.emit("Impossibile caricare i dati base")
                    return

            self.task_completed.emit("Dati base caricati")

            # Task 2: Creazione stagione (20%)
            self.progress_updated.emit(15, "Creazione stagione 2025/2026...")
            time.sleep(0.3)

            self.season = Season(2025)
            self.task_completed.emit("Stagione creata")

            # Task 3: Setup competizioni (40%)
            self.progress_updated.emit(25, "Setup campionati italiani...")
            time.sleep(0.4)

            # Prepara squadre per livello
            teams_by_level = {
                1: [team["nome"] for team in data_loader.get_serie_a_teams()],
                2: [team["nome"] for team in data_loader.get_serie_b_teams()]
            }

            # Ottieni gironi Serie C
            serie_c_groups = data_loader.get_serie_c_groups()

            self.progress_updated.emit(35, "Creazione competizioni...")
            self.competition_manager = setup_italian_competitions(
                self.season, teams_by_level, serie_c_groups
            )

            self.task_completed.emit(f"Competizioni create: {len(self.competition_manager.competitions)}")

            # Task 4: Generazione calendario (60%)
            self.progress_updated.emit(45, "Generazione calendario partite...")
            time.sleep(0.5)

            self.calendar = generate_italian_league_calendar(self.season, teams_by_level)

            self.progress_updated.emit(55, "Programmazione 1140+ partite...")
            time.sleep(0.3)

            summary = self.calendar.get_calendar_summary()
            self.task_completed.emit(f"Calendario generato: {summary['total_matches']} partite")

            # Task 5: Generazione giocatori (100%)
            self.progress_updated.emit(65, "Generazione giocatori per Serie A...")
            self._generate_players_for_level(teams_by_level[1], 1, 65, 80)

            self.progress_updated.emit(80, "Generazione giocatori per Serie B...")
            self._generate_players_for_level(teams_by_level[2], 2, 80, 90)

            self.progress_updated.emit(90, "Generazione giocatori per Serie C...")
            all_serie_c_teams = []
            for teams in serie_c_groups.values():
                all_serie_c_teams.extend(teams)
            self._generate_players_for_level(all_serie_c_teams, 3, 90, 100)

            self.task_completed.emit(f"Giocatori generati per {len(all_serie_c_teams + teams_by_level[1] + teams_by_level[2])} squadre")

            # Completamento
            self.progress_updated.emit(100, "Caricamento completato!")
            time.sleep(0.5)

            # Ritorna risultati
            result_data = {
                'season': self.season,
                'competition_manager': self.competition_manager,
                'calendar': self.calendar,
                'teams_data': self.teams_data
            }

            self.loading_completed.emit(result_data)

        except Exception as e:
            logger.error(f"Errore durante caricamento: {e}")
            self.loading_failed.emit(f"Errore: {str(e)}")

    def _generate_players_for_level(self, teams: list, level: int, start_progress: int, end_progress: int):
        """Genera giocatori per un livello di campionato"""
        if not teams:
            return

        progress_step = (end_progress - start_progress) / len(teams)

        for i, team_name in enumerate(teams):
            # Genera rosa
            squad = player_generator.generate_team_squad(team_level=level, squad_size=25)

            # Assegna squadra ai giocatori
            for player in squad:
                player.current_club = team_name

            self.teams_data[team_name] = squad

            # Aggiorna progresso
            current_progress = start_progress + int((i + 1) * progress_step)
            self.progress_updated.emit(current_progress, f"Generati giocatori per {team_name}...")

            time.sleep(0.1)  # Piccola pausa per mostrare progresso

class LoadingWidget(QWidget):
    """Widget di caricamento stile Football Manager"""

    # Segnali
    loading_completed = pyqtSignal(dict)  # dati caricati
    loading_cancelled = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
        self.setup_ui()

    def setup_ui(self):
        """Setup interfaccia"""
        self.setWindowTitle("Football Manager Italiano - Caricamento")
        self.setFixedSize(600, 400)
        self.setStyleSheet(f"background-color: {COLOR_BACKGROUND};")

        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)

        # Titolo
        title = QLabel("Football Manager Italiano")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {COLOR_PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Sottotitolo
        subtitle = QLabel("Preparazione stagione 2025/2026...")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet(f"""
            font-size: 14px;
            color: {COLOR_TEXT_SECONDARY};
            margin-bottom: 20px;
        """)
        layout.addWidget(subtitle)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: {COLOR_SURFACE};
                height: 30px;
            }}
            QProgressBar::chunk {{
                background-color: {COLOR_SUCCESS};
                border-radius: 6px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # Messaggio status
        self.status_label = QLabel("Pronto per il caricamento...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {COLOR_TEXT_SECONDARY};
            margin-top: 10px;
        """)
        layout.addWidget(self.status_label)

        # Log area
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(120)
        self.log_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: {COLOR_SURFACE};
                border: 1px solid {COLOR_BORDER};
                border-radius: 6px;
                font-family: 'Consolas', monospace;
                font-size: 10px;
                color: {COLOR_TEXT};
                padding: 10px;
            }}
        """)
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)

        # Bottoni
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.start_button = QPushButton("Inizia Nuova Stagione")
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #27AE60;
            }}
            QPushButton:disabled {{
                background-color: {COLOR_BORDER};
                color: {COLOR_TEXT_SECONDARY};
            }}
        """)
        self.start_button.clicked.connect(self.start_loading)
        button_layout.addWidget(self.start_button)

        self.cancel_button = QPushButton("Annulla")
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SURFACE};
                color: {COLOR_TEXT};
                border: 2px solid {COLOR_BORDER};
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_BORDER};
            }}
        """)
        self.cancel_button.clicked.connect(self.cancel_loading)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def start_loading(self):
        """Inizia il caricamento"""
        self.start_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_label.setText("Caricamento in corso...")
        self.log_area.clear()

        # Crea e avvia worker
        self.worker = SeasonLoadingWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.task_completed.connect(self.add_log)
        self.worker.loading_completed.connect(self.on_loading_completed)
        self.worker.loading_failed.connect(self.on_loading_failed)

        self.worker.start()

    def cancel_loading(self):
        """Annulla il caricamento"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()

        self.loading_cancelled.emit()
        self.close()

    def update_progress(self, value: int, message: str):
        """Aggiorna progress bar e messaggio"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def add_log(self, message: str):
        """Aggiunge messaggio al log"""
        self.log_area.append(f"✓ {message}")
        # Scroll to bottom
        scrollbar = self.log_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_loading_completed(self, data: dict):
        """Gestisce completamento caricamento"""
        self.add_log("CARICAMENTO COMPLETATO!")
        self.status_label.setText("Stagione pronta! Seleziona la tua squadra.")

        # Piccolo delay prima di emettere segnale
        QTimer.singleShot(1000, lambda: self.loading_completed.emit(data))

    def on_loading_failed(self, error_message: str):
        """Gestisce errore nel caricamento"""
        self.add_log(f"ERRORE: {error_message}")
        self.status_label.setText("Caricamento fallito.")
        self.start_button.setEnabled(True)
        self.start_button.setText("Riprova")

    def closeEvent(self, event):
        """Override close event per terminare worker"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        super().closeEvent(event)

# Test widget per sviluppo
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Test del widget
    loading_widget = LoadingWidget()

    def on_completed(data):
        print(f"Caricamento completato! Dati: {list(data.keys())}")
        app.quit()

    def on_cancelled():
        print("Caricamento annullato")
        app.quit()

    loading_widget.loading_completed.connect(on_completed)
    loading_widget.loading_cancelled.connect(on_cancelled)

    loading_widget.show()

    sys.exit(app.exec())