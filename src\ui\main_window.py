"""
Finestra principale di Football Manager Italiano
"""
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QStatusBar, QTabWidget, QLabel,
                            QSplitter, QFrame, QPushButton, QMessageBox,
                            QDialog, QFormLayout, QComboBox, QDialogButtonBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QFont

from ..core.config import *
from ..core.utils import logger, format_currency, format_date_italian
from ..data.data_loader import data_loader
from .squad_ui import SquadUI
from .competitions_ui import CompetitionsUI
from .loading_widget import LoadingWidget
from .continue_widget import ContinueWidget

class TeamSelectionDialog(QDialog):
    """Dialog per selezione squadra iniziale"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_team = None
        self.setup_ui()
        self.load_teams()

    def setup_ui(self):
        """Configura interfaccia dialog"""
        self.setWindowTitle("Seleziona la tua squadra")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # Titolo
        title_label = QLabel("Benvenuto in Football Manager Italiano!")
        title_font = QFont(MAIN_FONT_FAMILY, TITLE_FONT_SIZE, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Sottotitolo
        subtitle_label = QLabel("Scegli la squadra che vuoi dirigere:")
        layout.addWidget(subtitle_label)

        # Form per selezione
        form_layout = QFormLayout()

        # ComboBox livello
        self.level_combo = QComboBox()
        self.level_combo.addItems(["Serie A", "Serie B", "Serie C"])
        self.level_combo.currentTextChanged.connect(self.on_level_changed)
        form_layout.addRow("Livello:", self.level_combo)

        # ComboBox squadra
        self.team_combo = QComboBox()
        form_layout.addRow("Squadra:", self.team_combo)

        layout.addLayout(form_layout)

        # Info squadra selezionata
        self.team_info_label = QLabel()
        self.team_info_label.setWordWrap(True)
        layout.addWidget(self.team_info_label)

        layout.addStretch()

        # Bottoni
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Connetti cambio squadra
        self.team_combo.currentTextChanged.connect(self.on_team_changed)

    def load_teams(self):
        """Carica squadre disponibili"""
        # Carica Serie A di default
        self.on_level_changed("Serie A")

    def on_level_changed(self, level_text: str):
        """Gestisce cambio di livello"""
        self.team_combo.clear()

        level_mapping = {"Serie A": 1, "Serie B": 2, "Serie C": 3}
        level = level_mapping.get(level_text, 1)

        teams = data_loader.get_teams_by_level(level)

        for team in teams:
            team_name = team.get("nome", "Squadra Sconosciuta")
            self.team_combo.addItem(team_name)

        if teams:
            self.on_team_changed(self.team_combo.currentText())

    def on_team_changed(self, team_name: str):
        """Gestisce cambio squadra"""
        if not team_name:
            return

        team = data_loader.get_team_by_name(team_name)
        if team:
            info_text = self._format_team_info(team)
            self.team_info_label.setText(info_text)
            self.selected_team = team
        else:
            self.team_info_label.setText("Informazioni non disponibili")
            self.selected_team = None

    def _format_team_info(self, team: dict) -> str:
        """Formatta informazioni squadra"""
        nome = team.get("nome", "N/A")
        citta = team.get("citta", "N/A")
        stadio = team.get("stadio", "N/A")
        capienza = team.get("capienza_stadio", 0)
        reputazione = team.get("reputazione", 0)
        budget_trasferimenti = team.get("budget_trasferimenti_eur", 0)

        info = f"<b>{nome}</b><br>"
        info += f"Città: {citta}<br>"
        info += f"Stadio: {stadio} ({capienza:,} posti)<br>"
        info += f"Reputazione: {reputazione}/20<br>"
        info += f"Budget trasferimenti: {format_currency(budget_trasferimenti)}"

        return info

    def get_selected_team(self):
        """Restituisce squadra selezionata"""
        return self.selected_team

class MainWindow(QMainWindow):
    """Finestra principale dell'applicazione"""

    # Segnali
    team_selected = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.current_team = None
        self.game_state = {
            "current_date": "1 luglio 2025",
            "current_season": "2025/26",
            "current_matchday": 0
        }

        # Dati di gioco (caricati dal loading widget)
        self.season = None
        self.competition_manager = None
        self.calendar = None
        self.teams_data = {}

        self.setup_ui()
        self.setup_connections()

        # Nasconde finestra principale e mostra loading
        self.hide()
        self.show_loading_widget()

    def setup_ui(self):
        """Configura interfaccia utente"""
        self.setWindowTitle(WINDOW_TITLE)
        self.resize(WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT)
        self.setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)

        # Widget centrale
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principale
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Crea interfacce
        self.create_menu_bar()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()

    def create_menu_bar(self):
        """Crea menu bar"""
        menubar = self.menuBar()

        # Menu File
        file_menu = menubar.addMenu("File")

        new_game_action = QAction("Nuova Partita", self)
        new_game_action.triggered.connect(self.new_game)
        file_menu.addAction(new_game_action)

        load_game_action = QAction("Carica Partita", self)
        load_game_action.triggered.connect(self.load_game)
        file_menu.addAction(load_game_action)

        save_game_action = QAction("Salva Partita", self)
        save_game_action.triggered.connect(self.save_game)
        file_menu.addAction(save_game_action)

        file_menu.addSeparator()

        exit_action = QAction("Esci", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Menu Visualizza
        view_menu = menubar.addMenu("Visualizza")

        overview_action = QAction("Panoramica Club", self)
        overview_action.triggered.connect(lambda: self.switch_tab(0))
        view_menu.addAction(overview_action)

        squad_action = QAction("Squadra", self)
        squad_action.triggered.connect(lambda: self.switch_tab(1))
        view_menu.addAction(squad_action)

        # Menu Aiuto
        help_menu = menubar.addMenu("Aiuto")

        about_action = QAction("Informazioni", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """Crea toolbar"""
        toolbar = self.addToolBar("Principale")
        toolbar.setMovable(False)

        # Info data/stagione
        self.date_label = QLabel("Data: 1 luglio 2025")
        self.date_label.setStyleSheet(f"color: {COLOR_TEXT}; font-weight: bold;")
        toolbar.addWidget(self.date_label)

        toolbar.addSeparator()

        self.season_label = QLabel("Stagione: 2025/26")
        self.season_label.setStyleSheet(f"color: {COLOR_TEXT}; font-weight: bold;")
        toolbar.addWidget(self.season_label)

        toolbar.addSeparator()

    def create_main_content(self):
        """Crea contenuto principale"""
        # Splitter principale
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.centralWidget().layout().addWidget(main_splitter)

        # Panel sinistro (info squadra)
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # Panel centrale (tab principali)
        self.tab_widget = QTabWidget()
        self.create_main_tabs()
        main_splitter.addWidget(self.tab_widget)

        # Panel destro (notizie/eventi)
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # Imposta dimensioni splitter
        main_splitter.setSizes([250, 800, 250])

    def create_left_panel(self):
        """Crea panel sinistro con info squadra"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumWidth(280)

        layout = QVBoxLayout(panel)

        # Titolo
        title_label = QLabel("Il Tuo Club")
        title_font = QFont(MAIN_FONT_FAMILY, HEADER_FONT_SIZE, QFont.Weight.Bold)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Info squadra
        self.team_name_label = QLabel("Seleziona una squadra")
        team_font = QFont(MAIN_FONT_FAMILY, MAIN_FONT_SIZE + 2, QFont.Weight.Bold)
        self.team_name_label.setFont(team_font)
        layout.addWidget(self.team_name_label)

        self.team_info_widget = QLabel("Nessuna squadra selezionata")
        self.team_info_widget.setWordWrap(True)
        self.team_info_widget.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.addWidget(self.team_info_widget)

        layout.addStretch()

        return panel

    def create_right_panel(self):
        """Crea panel destro con Continue Widget"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumWidth(280)

        layout = QVBoxLayout(panel)

        # Continue Widget
        self.continue_widget = ContinueWidget()
        self.continue_widget.date_advanced.connect(self.on_date_advanced)
        self.continue_widget.matchday_simulated.connect(self.on_matchday_simulated)
        layout.addWidget(self.continue_widget)

        return panel

    def create_main_tabs(self):
        """Crea tab principali"""
        # Tab Panoramica
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        overview_label = QLabel("Panoramica Club")
        overview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        overview_layout.addWidget(overview_label)
        self.tab_widget.addTab(overview_tab, "Panoramica")

        # Tab Squadra
        self.squad_ui = SquadUI()
        self.tab_widget.addTab(self.squad_ui, "Squadra")

        # Tab Trasferimenti
        transfers_tab = QWidget()
        transfers_layout = QVBoxLayout(transfers_tab)
        transfers_label = QLabel("Mercato Trasferimenti")
        transfers_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        transfers_layout.addWidget(transfers_label)
        self.tab_widget.addTab(transfers_tab, "Trasferimenti")

        # Tab Finanze
        finances_tab = QWidget()
        finances_layout = QVBoxLayout(finances_tab)
        finances_label = QLabel("Gestione Finanziaria")
        finances_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        finances_layout.addWidget(finances_label)
        self.tab_widget.addTab(finances_tab, "Finanze")

        # Tab Competizioni
        self.competitions_ui = CompetitionsUI()
        self.tab_widget.addTab(self.competitions_ui, "Competizioni")

    def create_status_bar(self):
        """Crea status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Pronto")

    def setup_connections(self):
        """Configura connessioni segnali"""
        self.team_selected.connect(self.on_team_selected)

    def show_team_selection(self):
        """Mostra dialog selezione squadra"""
        dialog = TeamSelectionDialog(self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_team = dialog.get_selected_team()
            if selected_team:
                self.team_selected.emit(selected_team)
            else:
                QMessageBox.warning(self, "Selezione Squadra",
                                  "Nessuna squadra selezionata!")
                self.close()
        else:
            self.close()

    def on_team_selected(self, team: dict):
        """Gestisce selezione squadra"""
        self.current_team = team
        self.update_team_info()
        self.load_team_squad()
        self.status_bar.showMessage(f"Squadra selezionata: {team.get('nome', 'N/A')}")

    def update_team_info(self):
        """Aggiorna informazioni squadra nel panel sinistro"""
        if not self.current_team:
            return

        team_name = self.current_team.get("nome", "N/A")
        self.team_name_label.setText(team_name)

        info_text = self._format_detailed_team_info(self.current_team)
        self.team_info_widget.setText(info_text)

    def _format_detailed_team_info(self, team: dict) -> str:
        """Formatta info dettagliate squadra"""
        citta = team.get("citta", "N/A")
        stadio = team.get("stadio", "N/A")
        capienza = team.get("capienza_stadio", 0)
        reputazione = team.get("reputazione", 0)
        budget_trasferimenti = team.get("budget_trasferimenti_eur", 0)
        budget_stipendi = team.get("budget_stipendi_eur", 0)

        info = f"<b>Informazioni Club</b><br><br>"
        info += f"Città: {citta}<br>"
        info += f"Stadio: {stadio}<br>"
        info += f"Capienza: {capienza:,} posti<br>"
        info += f"Reputazione: {reputazione}/20<br><br>"
        info += f"<b>Budget</b><br>"
        info += f"Trasferimenti: {format_currency(budget_trasferimenti)}<br>"
        info += f"Stipendi: {format_currency(budget_stipendi)}<br>"

        return info

    def load_team_squad(self):
        """Carica la rosa della squadra selezionata"""
        if not self.current_team or not hasattr(self, 'teams_data'):
            logger.warning("Nessuna squadra selezionata o dati squadre non disponibili")
            return

        team_name = self.current_team.get("nome", "")
        if team_name in self.teams_data:
            squad = self.teams_data[team_name]
            logger.info(f"Caricamento rosa per {team_name}: {len(squad)} giocatori")
            self.squad_ui.load_squad(squad)
        else:
            logger.error(f"Rosa non trovata per la squadra: {team_name}")
            # Questo non dovrebbe mai accadere se il caricamento è andato a buon fine

    def switch_tab(self, index: int):
        """Cambia tab attivo"""
        self.tab_widget.setCurrentIndex(index)

    def on_date_advanced(self, days: int):
        """Gestisce avanzamento data"""
        if self.season:
            current_date = self.season.get_formatted_date()
            self.date_label.setText(f"Data: {current_date}")

            # Aggiorna anche la stagione se è cambiata
            season_text = f"Stagione: {self.season.start_year}/{str(self.season.start_year + 1)[-2:]}"
            self.season_label.setText(season_text)

            self.status_bar.showMessage(f"Avanzato di {days} giorni")
            logger.info(f"Data avanzata di {days} giorni: {current_date}")

    def on_matchday_simulated(self):
        """Gestisce simulazione giornata"""
        self.status_bar.showMessage("Giornata simulata")
        # Aggiorna le visualizzazioni delle competizioni
        if hasattr(self, 'competitions_ui'):
            self.competitions_ui.refresh_displays()
        logger.info("Giornata simulata")

    def new_game(self):
        """Nuova partita"""
        reply = QMessageBox.question(
            self, "Nuova Partita",
            "Vuoi iniziare una nuova partita?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.show_team_selection()

    def load_game(self):
        """Carica partita"""
        QMessageBox.information(self, "Carica Partita",
                               "Funzione non ancora implementata")

    def save_game(self):
        """Salva partita"""
        QMessageBox.information(self, "Salva Partita",
                               "Funzione non ancora implementata")

    def show_about(self):
        """Mostra informazioni"""
        QMessageBox.about(self, "Football Manager Italiano",
                         "Football Manager Italiano v1.0\n\n"
                         "Gioco manageriale di calcio\n"
                         "Sviluppato con PyQt6")

    def show_loading_widget(self):
        """Mostra widget di caricamento"""
        self.loading_widget = LoadingWidget(self)
        self.loading_widget.loading_completed.connect(self.on_loading_completed)
        self.loading_widget.loading_cancelled.connect(self.on_loading_cancelled)
        self.loading_widget.show()

    def on_loading_completed(self, data: dict):
        """Gestisce completamento caricamento"""
        # Salva dati caricati
        self.season = data.get('season')
        self.competition_manager = data.get('competition_manager')
        self.calendar = data.get('calendar')
        self.teams_data = data.get('teams_data', {})

        # Passa dati ai componenti UI
        if self.competition_manager:
            self.competitions_ui.load_season(self.season, self.competition_manager, self.calendar)
            self.continue_widget.load_season_data(self.season, self.competition_manager, self.calendar)

        # Chiude loading widget
        self.loading_widget.close()

        # Mostra finestra principale e dialog selezione squadra
        self.show()
        self.show_team_selection()

    def on_loading_cancelled(self):
        """Gestisce annullamento caricamento"""
        self.close()

    def closeEvent(self, event):
        """Gestisce chiusura finestra"""
        reply = QMessageBox.question(
            self, "Conferma Uscita",
            "Sei sicuro di voler uscire?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()