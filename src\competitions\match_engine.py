"""
Match Engine per simulazione partite avanzata
"""
import random
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..players.player import Player, PlayerPosition
from ..core.utils import logger, generate_match_attendance
from .calendar import Match

class MatchEvent(Enum):
    """Eventi partita"""
    GOAL = "Gol"
    YELLOW_CARD = "Cartellino Giallo"
    RED_CARD = "Cartellino Rosso"
    SUBSTITUTION = "Sostituzione"
    INJURY = "Infortunio"

@dataclass
class MatchPlayerEvent:
    """Evento di un giocatore nella partita"""
    minute: int
    event_type: MatchEvent
    player: Player
    description: str = ""

@dataclass
class MatchPlayerStats:
    """Statistiche giocatore in partita"""
    player: Player
    minutes_played: int = 0
    goals: int = 0
    assists: int = 0
    yellow_cards: int = 0
    red_cards: int = 0
    rating: float = 6.0
    passes_completed: int = 0
    passes_attempted: int = 0
    shots: int = 0
    shots_on_target: int = 0

class Formation:
    """Formazione tattica"""

    def __init__(self, formation_string: str = "4-4-2"):
        self.formation_string = formation_string
        self.positions = self._parse_formation(formation_string)

    def _parse_formation(self, formation: str) -> Dict[str, int]:
        """Analizza stringa formazione (es. 4-4-2)"""
        parts = formation.split('-')
        if len(parts) == 3:
            return {
                "defenders": int(parts[0]),
                "midfielders": int(parts[1]),
                "forwards": int(parts[2])
            }
        return {"defenders": 4, "midfielders": 4, "forwards": 2}

class TeamLineup:
    """Formazione squadra per una partita"""

    def __init__(self, team_name: str, available_players: List[Player]):
        self.team_name = team_name
        self.available_players = available_players
        self.formation = Formation()

        # Giocatori titolari (11)
        self.starting_eleven: List[Player] = []
        self.substitutes: List[Player] = []

        # Auto-genera formazione
        self._auto_select_lineup()

    def _auto_select_lineup(self):
        """Seleziona automaticamente formazione ottimale"""
        # Filtra giocatori disponibili
        available = [p for p in self.available_players if p.is_available]

        if len(available) < 11:
            logger.warning(f"{self.team_name}: Solo {len(available)} giocatori disponibili")
            # Usa comunque i disponibili
            self.starting_eleven = available[:11]
            self.substitutes = available[11:]
            return

        # Seleziona per posizione
        positions_needed = [
            (PlayerPosition.PORTIERE, 1),
            (PlayerPosition.DIFENSORE_CENTRALE, 2),
            (PlayerPosition.DIFENSORE_SINISTRO, 1),
            (PlayerPosition.DIFENSORE_DESTRO, 1),
            (PlayerPosition.CENTROCAMPISTA_CENTRALE, 2),
            (PlayerPosition.CENTROCAMPISTA_SINISTRO, 1),
            (PlayerPosition.CENTROCAMPISTA_DESTRO, 1),
            (PlayerPosition.ATTACCANTE_SINISTRO, 1),
            (PlayerPosition.CENTRAVANTI, 1)
        ]

        selected = []
        remaining = available.copy()

        for position, count in positions_needed:
            # Trova migliori giocatori per posizione
            position_players = [p for p in remaining if p.position == position]
            position_players.sort(key=lambda x: x.overall_rating, reverse=True)

            # Seleziona i migliori
            for i in range(min(count, len(position_players))):
                selected.append(position_players[i])
                remaining.remove(position_players[i])

        # Completa con migliori rimanenti se necessario
        remaining.sort(key=lambda x: x.overall_rating, reverse=True)
        while len(selected) < 11 and remaining:
            selected.append(remaining.pop(0))

        self.starting_eleven = selected
        self.substitutes = remaining[:7]  # Max 7 riserve

class MatchSimulator:
    """Simulatore partite avanzato"""

    def __init__(self):
        self.events_probability = {
            MatchEvent.GOAL: 0.15,
            MatchEvent.YELLOW_CARD: 0.25,
            MatchEvent.RED_CARD: 0.02,
            MatchEvent.INJURY: 0.05
        }

    def simulate_match(self, match: Match, home_players: List[Player],
                      away_players: List[Player]) -> Dict:
        """Simula partita con giocatori reali"""

        if match.played:
            return {"error": "Partita già giocata"}

        # Crea formazioni
        home_lineup = TeamLineup(match.home_team, home_players)
        away_lineup = TeamLineup(match.away_team, away_players)

        # Simula partita
        result = self._run_match_simulation(match, home_lineup, away_lineup)

        # Aggiorna match object
        match.home_score = result["home_score"]
        match.away_score = result["away_score"]
        match.played = True
        match.attendance = result["attendance"]

        return result

    def _run_match_simulation(self, match: Match, home_lineup: TeamLineup,
                             away_lineup: TeamLineup) -> Dict:
        """Esegue simulazione completa partita"""

        # Calcola forza squadre
        home_strength = self._calculate_team_strength(home_lineup.starting_eleven)
        away_strength = self._calculate_team_strength(away_lineup.starting_eleven)

        # Fattore casa
        home_strength *= 1.1

        # Simula eventi partita
        events = []
        home_goals = 0
        away_goals = 0

        # Statistiche giocatori
        home_stats = {p.id: MatchPlayerStats(p, 90) for p in home_lineup.starting_eleven}
        away_stats = {p.id: MatchPlayerStats(p, 90) for p in away_lineup.starting_eleven}

        # Simula 90 minuti
        for minute in range(1, 91):
            # Possibilità eventi
            if random.random() < 0.08:  # 8% chance evento ogni minuto
                event = self._generate_match_event(minute, home_lineup, away_lineup,
                                                 home_strength, away_strength)
                if event:
                    events.append(event)

                    # Aggiorna punteggio
                    if event.event_type == MatchEvent.GOAL:
                        if event.player in home_lineup.starting_eleven:
                            home_goals += 1
                            home_stats[event.player.id].goals += 1
                        else:
                            away_goals += 1
                            away_stats[event.player.id].goals += 1

                    # Aggiorna statistiche giocatore
                    if event.event_type == MatchEvent.YELLOW_CARD:
                        if event.player.id in home_stats:
                            home_stats[event.player.id].yellow_cards += 1
                        else:
                            away_stats[event.player.id].yellow_cards += 1

                    elif event.event_type == MatchEvent.RED_CARD:
                        if event.player.id in home_stats:
                            home_stats[event.player.id].red_cards += 1
                        else:
                            away_stats[event.player.id].red_cards += 1

        # Calcola affluenza
        home_team_reputation = max([p.overall_rating for p in home_lineup.starting_eleven])
        attendance = generate_match_attendance(50000, home_team_reputation)

        # Aggiorna statistiche giocatori
        self._update_player_season_stats(home_stats, away_stats)

        result = {
            "match_id": f"{match.home_team}_vs_{match.away_team}_md{match.matchday}",
            "matchday": match.matchday,
            "home_team": match.home_team,
            "away_team": match.away_team,
            "home_score": home_goals,
            "away_score": away_goals,
            "date": match.date,
            "attendance": attendance,
            "events": [self._event_to_dict(e) for e in events],
            "home_lineup": [p.full_name for p in home_lineup.starting_eleven],
            "away_lineup": [p.full_name for p in away_lineup.starting_eleven],
            "player_stats": {
                "home": [self._player_stats_to_dict(s) for s in home_stats.values()],
                "away": [self._player_stats_to_dict(s) for s in away_stats.values()]
            }
        }

        logger.info(f"Simulato: {match.home_team} {home_goals}-{away_goals} {match.away_team}")
        return result

    def _calculate_team_strength(self, players: List[Player]) -> float:
        """Calcola forza complessiva squadra"""
        if not players:
            return 50.0

        # Media overall rating pesata per posizione
        total_rating = sum(p.overall_rating for p in players)
        average_rating = total_rating / len(players)

        # Fattori aggiuntivi
        morale_factor = sum(p.morale for p in players) / len(players) / 100
        fitness_factor = sum(p.fitness for p in players) / len(players) / 100

        strength = average_rating * morale_factor * fitness_factor * 5
        return max(30.0, min(100.0, strength))

    def _generate_match_event(self, minute: int, home_lineup: TeamLineup,
                             away_lineup: TeamLineup, home_strength: float,
                             away_strength: float) -> Optional[MatchPlayerEvent]:
        """Genera evento casuale nella partita"""

        # Determina squadra che genera evento (basato su forza)
        total_strength = home_strength + away_strength
        home_chance = home_strength / total_strength

        if random.random() < home_chance:
            team_players = home_lineup.starting_eleven
            team_name = home_lineup.team_name
        else:
            team_players = away_lineup.starting_eleven
            team_name = away_lineup.team_name

        if not team_players:
            return None

        # Tipo evento basato su probabilità
        event_types = list(self.events_probability.keys())
        event_weights = list(self.events_probability.values())

        event_type = random.choices(event_types, weights=event_weights)[0]

        # Seleziona giocatore basato su evento
        if event_type == MatchEvent.GOAL:
            # Attaccanti più probabili per gol
            attackers = [p for p in team_players if p.position.value in ['AS', 'AD', 'C', 'T']]
            if attackers:
                weights = [p.attributes.tiro for p in attackers]
                player = random.choices(attackers, weights=weights)[0]
            else:
                player = random.choice(team_players)
        else:
            # Altri eventi casuali
            player = random.choice(team_players)

        description = self._generate_event_description(event_type, player, team_name)

        return MatchPlayerEvent(
            minute=minute,
            event_type=event_type,
            player=player,
            description=description
        )

    def _generate_event_description(self, event_type: MatchEvent,
                                   player: Player, team_name: str) -> str:
        """Genera descrizione evento"""
        descriptions = {
            MatchEvent.GOAL: f"Gol di {player.full_name} ({team_name})!",
            MatchEvent.YELLOW_CARD: f"Cartellino giallo per {player.full_name} ({team_name})",
            MatchEvent.RED_CARD: f"Cartellino rosso per {player.full_name} ({team_name})!",
            MatchEvent.INJURY: f"Infortunio per {player.full_name} ({team_name})"
        }
        return descriptions.get(event_type, f"Evento per {player.full_name}")

    def _update_player_season_stats(self, home_stats: Dict, away_stats: Dict):
        """Aggiorna statistiche stagionali giocatori"""
        for stats in list(home_stats.values()) + list(away_stats.values()):
            player = stats.player
            player.current_season_stats.add_match_stats(
                minutes=stats.minutes_played,
                goals=stats.goals,
                assists=stats.assists,
                yellow_cards=stats.yellow_cards,
                red_cards=stats.red_cards,
                rating=stats.rating
            )

    def _event_to_dict(self, event: MatchPlayerEvent) -> Dict:
        """Converte evento in dizionario"""
        return {
            "minute": event.minute,
            "event_type": event.event_type.value,
            "player": event.player.full_name,
            "description": event.description
        }

    def _player_stats_to_dict(self, stats: MatchPlayerStats) -> Dict:
        """Converte statistiche giocatore in dizionario"""
        return {
            "player": stats.player.full_name,
            "position": stats.player.position.value,
            "minutes": stats.minutes_played,
            "goals": stats.goals,
            "assists": stats.assists,
            "yellow_cards": stats.yellow_cards,
            "red_cards": stats.red_cards,
            "rating": round(stats.rating, 1)
        }

# Istanza globale del simulatore
match_simulator = MatchSimulator()