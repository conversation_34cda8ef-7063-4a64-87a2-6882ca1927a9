"""
Widget Continue per Football Manager Italian<PERSON>
Gestisce l'avanzamento del tempo e la simulazione delle giornate
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QProgressBar, QFrame, QComboBox, QGroupBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.config import *
from ..core.utils import logger, format_date_italian
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager
from ..competitions.calendar import MatchCalendar

class ContinueWidget(QWidget):
    """Widget per controllo avanzamento tempo stile Football Manager"""
    
    # Segnali
    date_advanced = pyqtSignal(int)  # giorni avanzati
    matchday_simulated = pyqtSignal()  # giornata simulata
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.season = None
        self.competition_manager = None
        self.calendar = None
        
        # Stato simulazione
        self.is_running = False
        self.simulation_speed = 1  # 1=normale, 2=veloce, 3=molto veloce
        
        # Timer per simulazione automatica
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.advance_time_step)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Configura interfaccia widget"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Header con data corrente
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setStyleSheet(f"background-color: {COLOR_PRIMARY}; color: white; border-radius: 5px;")
        header_layout = QVBoxLayout(header_frame)
        
        # Data corrente
        self.current_date_label = QLabel("1 Luglio 2025")
        date_font = QFont("Arial", 14, QFont.Weight.Bold)
        self.current_date_label.setFont(date_font)
        self.current_date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.current_date_label)
        
        # Stagione
        self.season_label = QLabel("Stagione 2025/26")
        season_font = QFont("Arial", 10)
        self.season_label.setFont(season_font)
        self.season_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.season_label)
        
        layout.addWidget(header_frame)
        
        # Prossimo evento
        event_group = QGroupBox("Prossimo Evento")
        event_layout = QVBoxLayout(event_group)
        
        self.next_event_label = QLabel("Nessun evento programmato")
        self.next_event_label.setWordWrap(True)
        event_layout.addWidget(self.next_event_label)
        
        self.days_to_event_label = QLabel("Giorni: 0")
        event_layout.addWidget(self.days_to_event_label)
        
        layout.addWidget(event_group)
        
        # Controlli simulazione
        controls_group = QGroupBox("Controlli")
        controls_layout = QVBoxLayout(controls_group)
        
        # Bottone Continue principale
        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #45a049;
            }}
            QPushButton:pressed {{
                background-color: #3d8b40;
            }}
        """)
        self.continue_button.clicked.connect(self.toggle_simulation)
        controls_layout.addWidget(self.continue_button)
        
        # Controlli velocità
        speed_layout = QHBoxLayout()
        
        speed_label = QLabel("Velocità:")
        speed_layout.addWidget(speed_label)
        
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["Normale", "Veloce", "Molto Veloce"])
        self.speed_combo.currentIndexChanged.connect(self.on_speed_changed)
        speed_layout.addWidget(self.speed_combo)
        
        controls_layout.addLayout(speed_layout)
        
        # Bottoni azioni rapide
        quick_actions_layout = QHBoxLayout()
        
        self.advance_day_button = QPushButton("Avanza 1 Giorno")
        self.advance_day_button.clicked.connect(lambda: self.advance_days(1))
        quick_actions_layout.addWidget(self.advance_day_button)
        
        self.advance_week_button = QPushButton("Avanza 1 Settimana")
        self.advance_week_button.clicked.connect(lambda: self.advance_days(7))
        quick_actions_layout.addWidget(self.advance_week_button)
        
        controls_layout.addLayout(quick_actions_layout)
        
        # Bottone simula prossima giornata
        self.simulate_matchday_button = QPushButton("Simula Prossima Giornata")
        self.simulate_matchday_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_WARNING};
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: #e68900;
            }}
        """)
        self.simulate_matchday_button.clicked.connect(self.simulate_next_matchday)
        controls_layout.addWidget(self.simulate_matchday_button)
        
        layout.addWidget(controls_group)
        
        # Progress bar stagione
        progress_group = QGroupBox("Progresso Stagione")
        progress_layout = QVBoxLayout(progress_group)
        
        self.season_progress = QProgressBar()
        self.season_progress.setRange(0, 100)
        self.season_progress.setValue(0)
        progress_layout.addWidget(self.season_progress)
        
        self.progress_label = QLabel("0% completato")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        layout.addStretch()
        
    def load_season_data(self, season: Season, competition_manager: CompetitionManager, 
                        calendar: MatchCalendar):
        """Carica i dati della stagione"""
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar
        
        self.update_display()
        logger.info("Dati stagione caricati nel Continue Widget")
        
    def update_display(self):
        """Aggiorna la visualizzazione"""
        if not self.season:
            return
            
        # Aggiorna data corrente
        current_date = self.season.get_formatted_date()
        self.current_date_label.setText(current_date)
        
        # Aggiorna stagione
        season_text = f"Stagione {self.season.start_year}/{str(self.season.start_year + 1)[-2:]}"
        self.season_label.setText(season_text)
        
        # Aggiorna progresso stagione
        progress = self.season.season_progress_percentage
        self.season_progress.setValue(int(progress))
        self.progress_label.setText(f"{progress:.1f}% completato")
        
        # Aggiorna prossimo evento
        self.update_next_event()
        
    def update_next_event(self):
        """Aggiorna informazioni sul prossimo evento"""
        if not self.calendar:
            self.next_event_label.setText("Nessun evento programmato")
            self.days_to_event_label.setText("Giorni: 0")
            return
            
        # Trova prossima partita
        next_matches = self.calendar.get_next_matches(limit=1)
        if next_matches:
            next_match = next_matches[0]
            event_text = f"{next_match.home_team} vs {next_match.away_team}"

            # Aggiungi data partita
            match_date = next_match.date.strftime("%d/%m/%Y")
            event_text += f"\n{match_date}"

            # Aggiungi competizione se disponibile
            if hasattr(next_match, 'competition') and next_match.competition:
                event_text += f"\n{next_match.competition}"

            self.next_event_label.setText(event_text)

            # Calcola giorni rimanenti
            if self.season:
                days_diff = (next_match.date - self.season.current_date).days
                self.days_to_event_label.setText(f"Giorni: {max(0, days_diff)}")
        else:
            self.next_event_label.setText("Nessuna partita programmata")
            self.days_to_event_label.setText("Giorni: 0")
            
    def toggle_simulation(self):
        """Avvia/ferma simulazione automatica"""
        if self.is_running:
            self.stop_simulation()
        else:
            self.start_simulation()
            
    def start_simulation(self):
        """Avvia simulazione automatica"""
        self.is_running = True
        self.continue_button.setText("Pausa")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_ERROR};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        
        # Avvia timer basato sulla velocità
        intervals = {1: 2000, 2: 1000, 3: 500}  # millisecondi
        self.simulation_timer.start(intervals.get(self.simulation_speed, 2000))
        
        logger.info(f"Simulazione avviata (velocità {self.simulation_speed})")
        
    def stop_simulation(self):
        """Ferma simulazione automatica"""
        self.is_running = False
        self.continue_button.setText("Continue")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #45a049;
            }}
        """)
        
        self.simulation_timer.stop()
        logger.info("Simulazione fermata")
        
    def advance_time_step(self):
        """Avanza di un passo temporale durante la simulazione"""
        if not self.season:
            return
            
        # Avanza di 1 giorno
        self.advance_days(1)
        
    def advance_days(self, days: int):
        """Avanza di un numero specifico di giorni"""
        if not self.season:
            logger.warning("Nessuna stagione caricata")
            return
            
        # Avanza la stagione
        events = self.season.advance_date(days)
        
        # Aggiorna display
        self.update_display()
        
        # Emetti segnale
        self.date_advanced.emit(days)
        
        # Log eventi se presenti
        if events:
            for event in events:
                logger.info(f"Evento stagione: {event}")
                
    def simulate_next_matchday(self):
        """Simula la prossima giornata di campionato"""
        if not self.competition_manager:
            logger.warning("Nessun competition manager disponibile")
            return
            
        # Trova competizioni attive
        active_competitions = []
        for comp in self.competition_manager.get_all_competitions():
            if hasattr(comp, 'current_matchday') and not comp.is_season_completed():
                active_competitions.append(comp)
                
        if not active_competitions:
            logger.info("Nessuna competizione attiva da simulare")
            return
            
        # Simula giornata per ogni competizione attiva
        total_matches = 0
        for comp in active_competitions:
            next_matchday = comp.current_matchday + 1
            results = comp.simulate_matchday(next_matchday)
            total_matches += len(results)
            
        logger.info(f"Simulata giornata: {total_matches} partite")
        
        # Aggiorna display
        self.update_display()
        
        # Emetti segnale
        self.matchday_simulated.emit()
        
    def on_speed_changed(self, index: int):
        """Gestisce cambio velocità simulazione"""
        self.simulation_speed = index + 1
        
        # Se la simulazione è in corso, riavvia con nuova velocità
        if self.is_running:
            self.simulation_timer.stop()
            intervals = {1: 2000, 2: 1000, 3: 500}
            self.simulation_timer.start(intervals.get(self.simulation_speed, 2000))
            
        logger.debug(f"Velocità simulazione cambiata: {self.simulation_speed}")
