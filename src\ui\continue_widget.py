"""
Widget Continue per Football Manager Italian<PERSON>
Gestisce l'avanzamento del tempo e la simulazione delle giornate
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QProgressBar, QFrame, QComboBox, QGroupBox, QDialog)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.config import *
from ..core.utils import logger, format_date_italian
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager
from ..competitions.calendar import MatchCalendar
from .advanced_calendar_widget import AdvancedCalendarWidget

class ContinueWidget(QWidget):
    """Widget per controllo avanzamento tempo stile Football Manager"""
    
    # Segnali
    date_advanced = pyqtSignal(int)  # giorni avanzati
    matchday_simulated = pyqtSignal()  # giornata simulata
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.season = None
        self.competition_manager = None
        self.calendar = None
        
        # Stato simulazione
        self.is_running = False
        self.simulation_speed = 1  # 1=normale, 2=veloce, 3=molto veloce
        
        # Timer per simulazione automatica
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.advance_time_step)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Configura interfaccia widget"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Header con data corrente
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setStyleSheet(f"background-color: {COLOR_PRIMARY}; color: white; border-radius: 5px;")
        header_layout = QVBoxLayout(header_frame)
        
        # Data corrente
        self.current_date_label = QLabel("1 Luglio 2025")
        date_font = QFont("Arial", 14, QFont.Weight.Bold)
        self.current_date_label.setFont(date_font)
        self.current_date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.current_date_label)
        
        # Stagione
        self.season_label = QLabel("Stagione 2025/26")
        season_font = QFont("Arial", 10)
        self.season_label.setFont(season_font)
        self.season_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.season_label)
        
        layout.addWidget(header_frame)
        
        # Prossimo evento
        event_group = QGroupBox("Prossimo Evento")
        event_layout = QVBoxLayout(event_group)
        
        self.next_event_label = QLabel("Nessun evento programmato")
        self.next_event_label.setWordWrap(True)
        event_layout.addWidget(self.next_event_label)
        
        self.days_to_event_label = QLabel("Giorni: 0")
        event_layout.addWidget(self.days_to_event_label)
        
        layout.addWidget(event_group)
        
        # Controlli simulazione
        controls_group = QGroupBox("Controlli")
        controls_layout = QVBoxLayout(controls_group)
        
        # Bottone Continue principale
        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #45a049;
            }}
            QPushButton:pressed {{
                background-color: #3d8b40;
            }}
        """)
        self.continue_button.clicked.connect(self.toggle_simulation)
        controls_layout.addWidget(self.continue_button)
        
        # Controlli velocità
        speed_layout = QHBoxLayout()
        
        speed_label = QLabel("Velocità:")
        speed_layout.addWidget(speed_label)
        
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["Normale", "Veloce", "Molto Veloce"])
        self.speed_combo.currentIndexChanged.connect(self.on_speed_changed)
        speed_layout.addWidget(self.speed_combo)
        
        controls_layout.addLayout(speed_layout)
        
        # Bottone calendario avanzato
        self.calendar_button = QPushButton("📅 Apri Calendario Stagionale")
        self.calendar_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_PRIMARY};
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #5a6fd8;
            }}
        """)
        self.calendar_button.clicked.connect(self.open_advanced_calendar)
        controls_layout.addWidget(self.calendar_button)

        # Separatore
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        controls_layout.addWidget(separator)

        # Bottoni azioni rapide (semplificati)
        quick_actions_layout = QHBoxLayout()

        self.advance_week_button = QPushButton("Avanza 1 Settimana")
        self.advance_week_button.clicked.connect(lambda: self.advance_days(7))
        quick_actions_layout.addWidget(self.advance_week_button)

        self.advance_month_button = QPushButton("Avanza 1 Mese")
        self.advance_month_button.clicked.connect(lambda: self.advance_days(30))
        quick_actions_layout.addWidget(self.advance_month_button)

        controls_layout.addLayout(quick_actions_layout)
        
        layout.addWidget(controls_group)
        
        # Progress bar stagione
        progress_group = QGroupBox("Progresso Stagione")
        progress_layout = QVBoxLayout(progress_group)
        
        self.season_progress = QProgressBar()
        self.season_progress.setRange(0, 100)
        self.season_progress.setValue(0)
        progress_layout.addWidget(self.season_progress)
        
        self.progress_label = QLabel("0% completato")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        layout.addStretch()
        
    def load_season_data(self, season: Season, competition_manager: CompetitionManager, 
                        calendar: MatchCalendar):
        """Carica i dati della stagione"""
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar
        
        self.update_display()
        logger.info("Dati stagione caricati nel Continue Widget")
        
    def update_display(self):
        """Aggiorna la visualizzazione"""
        if not self.season:
            return
            
        # Aggiorna data corrente
        current_date = self.season.get_formatted_date()
        self.current_date_label.setText(current_date)
        
        # Aggiorna stagione
        season_text = f"Stagione {self.season.start_year}/{str(self.season.start_year + 1)[-2:]}"
        self.season_label.setText(season_text)
        
        # Aggiorna progresso stagione
        progress = self.season.season_progress_percentage
        self.season_progress.setValue(int(progress))
        self.progress_label.setText(f"{progress:.1f}% completato")
        
        # Aggiorna prossimo evento e bottone Continue
        self.update_next_event()
        self.update_continue_button()
        
    def update_next_event(self):
        """Aggiorna informazioni sul prossimo evento"""
        if not self.calendar:
            self.next_event_label.setText("Nessun evento programmato")
            self.days_to_event_label.setText("Giorni: 0")
            return

        # Usa il nuovo metodo get_next_important_event
        next_event = self.calendar.get_next_important_event()
        if next_event:
            event_text = next_event["description"]

            # Aggiungi data partita
            match_date = next_event["date"].strftime("%d/%m/%Y")
            event_text += f"\n{match_date}"

            # Aggiungi competizione
            if next_event["competition"]:
                event_text += f"\n{next_event['competition']}"

            self.next_event_label.setText(event_text)
            self.days_to_event_label.setText(f"Giorni: {next_event['days_to_event']}")
        else:
            self.next_event_label.setText("Nessuna partita programmata")
            self.days_to_event_label.setText("Giorni: 0")

    def update_continue_button(self):
        """Aggiorna testo del bottone Continue con anteprima evento"""
        if not self.calendar or self.is_running:
            return

        next_event = self.calendar.get_next_important_event()
        if next_event:
            if next_event["days_to_event"] == 0:
                button_text = f"Continue to: {next_event['description']}"
            else:
                button_text = f"Continue to: {next_event['description']} ({next_event['days_to_event']} giorni)"
        else:
            button_text = "Continue"

        self.continue_button.setText(button_text)
            
    def toggle_simulation(self):
        """Avvia/ferma simulazione automatica"""
        if self.is_running:
            self.stop_simulation()
        else:
            self.start_simulation()
            
    def start_simulation(self):
        """Avvia simulazione automatica"""
        self.is_running = True
        self.continue_button.setText("Pausa")
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_ERROR};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        
        # Avvia timer basato sulla velocità
        intervals = {1: 2000, 2: 1000, 3: 500}  # millisecondi
        self.simulation_timer.start(intervals.get(self.simulation_speed, 2000))
        
        logger.info(f"Simulazione avviata (velocità {self.simulation_speed})")
        
    def stop_simulation(self):
        """Ferma simulazione automatica"""
        self.is_running = False

        # Ripristina stile e testo dinamico del bottone
        self.continue_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS};
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #45a049;
            }}
        """)

        # Aggiorna testo con anteprima evento
        self.update_continue_button()

        self.simulation_timer.stop()
        logger.info("Simulazione fermata")
        
    def advance_time_step(self):
        """Avanza al prossimo evento importante durante la simulazione"""
        if not self.season or not self.calendar:
            return

        next_event = self.calendar.get_next_important_event()
        if next_event:
            days_to_advance = max(1, next_event["days_to_event"])
            self.advance_days(days_to_advance)
        else:
            # Fallback: avanza di 1 giorno se non ci sono eventi
            self.advance_days(1)
        
    def advance_days(self, days: int):
        """Avanza di un numero specifico di giorni"""
        if not self.season:
            logger.warning("Nessuna stagione caricata")
            return

        # Salva data iniziale
        start_date = self.season.current_date

        # Avanza la stagione
        events = self.season.advance_date(days)

        # Controlla se ci sono partite da simulare nel periodo avanzato
        if self.calendar and self.competition_manager:
            # Ottieni tutte le partite nel range di date avanzate
            end_date = self.season.current_date
            matches_in_range = self.calendar.get_matches_in_date_range(start_date, end_date)

            # Simula automaticamente le partite non ancora giocate
            matches_simulated = 0
            for match in matches_in_range:
                if not match.played and match.date <= end_date:
                    # Trova la competizione per questa partita
                    competition = self.competition_manager.get_competition(match.competition)
                    if competition and hasattr(competition, 'simulate_match'):
                        result = competition.simulate_match(match)
                        if "error" not in result:
                            matches_simulated += 1
                            logger.debug(f"Auto-simulata: {match.home_team} vs {match.away_team}")

            if matches_simulated > 0:
                logger.info(f"Auto-simulate {matches_simulated} partite durante avanzamento date")

        # Aggiorna display
        self.update_display()

        # Emetti segnale
        self.date_advanced.emit(days)

        # Log eventi se presenti
        if events:
            for event in events:
                logger.info(f"Evento stagione: {event}")

        # Se la simulazione continua è in pausa, aggiorna il testo del bottone
        if not self.is_running:
            self.update_continue_button()
                
        
    def on_speed_changed(self, index: int):
        """Gestisce cambio velocità simulazione"""
        self.simulation_speed = index + 1
        
        # Se la simulazione è in corso, riavvia con nuova velocità
        if self.is_running:
            self.simulation_timer.stop()
            intervals = {1: 2000, 2: 1000, 3: 500}
            self.simulation_timer.start(intervals.get(self.simulation_speed, 2000))
            
        logger.debug(f"Velocità simulazione cambiata: {self.simulation_speed}")

    def open_advanced_calendar(self):
        """Apre il widget calendario avanzato"""
        if not self.season or not self.calendar or not self.competition_manager:
            logger.warning("Dati non disponibili per il calendario")
            return

        # Crea e mostra calendario
        calendar_widget = AdvancedCalendarWidget(self)
        calendar_widget.load_data(self.season, self.calendar, self.competition_manager)

        # Collega segnali
        calendar_widget.simulation_requested.connect(self.on_calendar_simulation_completed)

        # Mostra come dialog
        result = calendar_widget.exec()

        if result == QDialog.DialogCode.Accepted:
            logger.info("Calendario chiuso con successo")

    def on_calendar_simulation_completed(self, target_date):
        """Gestisce completamento simulazione da calendario"""
        logger.info(f"Simulazione calendario completata fino al {target_date}")

        # Aggiorna display principale
        self.update_display()

        # Emetti segnale per aggiornare altre componenti
        days_advanced = (target_date - self.season.current_date).days
        self.date_advanced.emit(days_advanced)
