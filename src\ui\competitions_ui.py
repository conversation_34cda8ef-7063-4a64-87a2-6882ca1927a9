"""
Interfaccia utente per competizioni e calendari
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLabel, QPushButton,
                            QTabWidget, QGroupBox, QSplitter, QTextEdit,
                            QComboBox, QProgressBar)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from ..core.config import COLOR_PRIMARY, COLOR_SUCCESS, COLOR_WARNING, COLOR_ERROR, HEADER_FONT_SIZE
from ..core.utils import format_currency, format_date_italian
from ..competitions.season import Season
from ..competitions.competition import CompetitionManager, LeagueCompetition, LeagueStanding
from ..competitions.calendar import MatchCalendar, Match
from typing import List, Optional, Dict

class StandingsTableWidget(QTableWidget):
    """Tabella classifiche"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configura tabella classifiche"""
        headers = ["Pos", "Squadra", "G", "V", "N", "S", "GF", "GS", "DR", "Pt"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSortingEnabled(False)  # Ordinamento manuale

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nome squadra

    def load_standings(self, standings: List[LeagueStanding],
                      promotion_spots: int = 0, relegation_spots: int = 0):
        """Carica classifica"""
        self.setRowCount(len(standings))

        for row, standing in enumerate(standings):
            # Posizione
            pos_item = QTableWidgetItem(str(standing.position))
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, pos_item)

            # Nome squadra
            team_item = QTableWidgetItem(standing.team_name)
            self.setItem(row, 1, team_item)

            # Statistiche
            stats = [
                str(standing.matches_played),
                str(standing.wins),
                str(standing.draws),
                str(standing.losses),
                str(standing.goals_for),
                str(standing.goals_against),
                str(standing.goal_difference),
                str(standing.points)
            ]

            for col, stat in enumerate(stats, start=2):
                item = QTableWidgetItem(stat)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.setItem(row, col, item)

            # Colora righe per zone
            if promotion_spots > 0 and standing.position <= promotion_spots:
                # Zona promozione (verde)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.green)
            elif relegation_spots > 0 and standing.position > (len(standings) - relegation_spots):
                # Zona retrocessione (rosso)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.red)
            elif hasattr(standing, 'playoff_zone') and standing.playoff_zone:
                # Zona playoff (giallo)
                for col in range(self.columnCount()):
                    self.item(row, col).setBackground(Qt.GlobalColor.yellow)

class MatchesTableWidget(QTableWidget):
    """Tabella partite"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """Configura tabella partite"""
        headers = ["Data", "Ora", "Casa", "", "Trasferta", "Comp", "Spettatori"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # Ridimensiona colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Casa
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # Trasferta

    def load_matches(self, matches: List[Match]):
        """Carica partite"""
        self.setRowCount(len(matches))

        for row, match in enumerate(matches):
            # Data
            date_item = QTableWidgetItem(match.date.strftime("%d/%m"))
            date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, date_item)

            # Ora
            time_item = QTableWidgetItem(match.time)
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 1, time_item)

            # Casa
            home_item = QTableWidgetItem(match.home_team)
            self.setItem(row, 2, home_item)

            # Risultato
            result_item = QTableWidgetItem(match.result_string)
            result_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if match.played:
                result_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            self.setItem(row, 3, result_item)

            # Trasferta
            away_item = QTableWidgetItem(match.away_team)
            self.setItem(row, 4, away_item)

            # Competizione
            comp_item = QTableWidgetItem(match.competition[:10])  # Abbrevia
            comp_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 5, comp_item)

            # Spettatori
            attendance = str(match.attendance) if match.attendance else ""
            att_item = QTableWidgetItem(attendance)
            att_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 6, att_item)

class SeasonInfoWidget(QWidget):
    """Widget informazioni stagione"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.setup_ui()

    def setup_ui(self):
        """Configura interfaccia"""
        layout = QVBoxLayout(self)

        # Titolo stagione
        self.season_label = QLabel("Stagione 2025/26")
        season_font = QFont("Arial", HEADER_FONT_SIZE, QFont.Weight.Bold)
        self.season_label.setFont(season_font)
        self.season_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.season_label)

        # Informazioni attuali
        info_group = QGroupBox("Informazioni Attuali")
        info_layout = QVBoxLayout(info_group)

        self.current_date_label = QLabel("Data: 1 luglio 2025")
        info_layout.addWidget(self.current_date_label)

        self.current_phase_label = QLabel("Fase: Preparazione")
        info_layout.addWidget(self.current_phase_label)

        self.current_matchday_label = QLabel("Giornata: 0/38")
        info_layout.addWidget(self.current_matchday_label)

        # Progress bar stagione
        self.season_progress = QProgressBar()
        self.season_progress.setRange(0, 100)
        info_layout.addWidget(QLabel("Progresso stagione:"))
        info_layout.addWidget(self.season_progress)

        layout.addWidget(info_group)

        # Mercato
        market_group = QGroupBox("Finestra di Mercato")
        market_layout = QVBoxLayout(market_group)

        self.transfer_window_label = QLabel("Mercato: Chiuso")
        market_layout.addWidget(self.transfer_window_label)

        self.transfer_days_label = QLabel("Giorni rimasti: 0")
        market_layout.addWidget(self.transfer_days_label)

        layout.addWidget(market_group)

        # Prossimi eventi
        events_group = QGroupBox("Prossimi Eventi")
        events_layout = QVBoxLayout(events_group)

        self.next_event_label = QLabel("Nessun evento programmato")
        self.next_event_label.setWordWrap(True)
        events_layout.addWidget(self.next_event_label)

        layout.addWidget(events_group)

        layout.addStretch()

    def update_season_info(self, season: Season):
        """Aggiorna informazioni stagione"""
        self.season = season

        # Informazioni base
        self.season_label.setText(f"Stagione {season.season_name}")
        self.current_date_label.setText(f"Data: {season.get_formatted_date()}")
        self.current_phase_label.setText(f"Fase: {season.current_phase.value}")
        self.current_matchday_label.setText(f"Giornata: {season.current_matchday}/{season.max_matchdays}")

        # Progress
        progress = int(season.season_progress_percentage)
        self.season_progress.setValue(progress)
        self.season_progress.setFormat(f"{progress}%")

        # Mercato
        transfer_window = season.get_transfer_window()
        self.transfer_window_label.setText(f"Mercato: {transfer_window.value}")

        days_left = season.transfer_window_days_left
        if days_left > 0:
            self.transfer_days_label.setText(f"Giorni rimasti: {days_left}")
            self.transfer_window_label.setStyleSheet(f"color: {COLOR_SUCCESS}; font-weight: bold;")
        else:
            self.transfer_days_label.setText("Mercato chiuso")
            self.transfer_window_label.setStyleSheet(f"color: {COLOR_ERROR};")

        # Prossimo evento
        next_date, next_event = season.get_next_important_date()
        days_to_event = (next_date - season.current_date).days
        self.next_event_label.setText(f"{next_event}\n{format_date_italian(next_date)}\n({days_to_event} giorni)")

class CompetitionsUI(QWidget):
    """Interfaccia principale competizioni"""

    simulate_matchday_requested = pyqtSignal()  # Segnale per simulare giornata

    def __init__(self, parent=None):
        super().__init__(parent)
        self.season: Optional[Season] = None
        self.competition_manager: Optional[CompetitionManager] = None
        self.calendar: Optional[MatchCalendar] = None
        self.setup_ui()

        # Timer per aggiornamenti automatici
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_displays)
        self.update_timer.start(30000)  # Aggiorna ogni 30 secondi

    def setup_ui(self):
        """Configura interfaccia"""
        layout = QVBoxLayout(self)

        # Header con controlli
        header_layout = QHBoxLayout()

        title_label = QLabel("Competizioni")
        title_font = QFont("Arial", HEADER_FONT_SIZE + 2, QFont.Weight.Bold)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Bottone simula giornata
        self.simulate_button = QPushButton("▶ Simula Giornata")
        self.simulate_button.setStyleSheet(f"background-color: {COLOR_PRIMARY}; color: white; font-weight: bold;")
        self.simulate_button.clicked.connect(self.simulate_matchday_requested.emit)
        header_layout.addWidget(self.simulate_button)

        layout.addLayout(header_layout)

        # Splitter principale
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Panel sinistro - Info stagione
        self.season_info = SeasonInfoWidget()
        main_splitter.addWidget(self.season_info)

        # Panel centrale - Tab competizioni
        self.competitions_tabs = QTabWidget()
        self.create_competition_tabs()
        main_splitter.addWidget(self.competitions_tabs)

        # Panel destro - Prossime partite
        right_widget = self.create_matches_panel()
        main_splitter.addWidget(right_widget)

        # Dimensioni splitter
        main_splitter.setSizes([250, 600, 300])

        layout.addWidget(main_splitter)

        # Test button per generare dati
        test_layout = QHBoxLayout()
        test_layout.addStretch()

        self.generate_test_season_button = QPushButton("Genera Stagione Test")
        self.generate_test_season_button.clicked.connect(self.generate_test_season)
        test_layout.addWidget(self.generate_test_season_button)

        layout.addLayout(test_layout)

    def create_competition_tabs(self):
        """Crea tab per competizioni"""
        # Tab Serie A
        self.serie_a_standings = StandingsTableWidget()
        self.competitions_tabs.addTab(self.serie_a_standings, "Serie A")

        # Tab Serie B
        self.serie_b_standings = StandingsTableWidget()
        self.competitions_tabs.addTab(self.serie_b_standings, "Serie B")

        # Tab Serie C - 3 gironi
        self.serie_c_standings = {}
        for girone in ["A", "B", "C"]:
            girone_widget = StandingsTableWidget()
            self.serie_c_standings[f"Girone {girone}"] = girone_widget
            self.competitions_tabs.addTab(girone_widget, f"Serie C {girone}")

        # Tab Coppe
        self.cups_widget = QWidget()
        cups_layout = QVBoxLayout(self.cups_widget)

        # Sezione Coppa Italia
        coppa_italia_label = QLabel("Coppa Italia")
        coppa_italia_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2E7D32;")
        cups_layout.addWidget(coppa_italia_label)

        self.coppa_italia_info = QLabel("In attesa di generazione...")
        cups_layout.addWidget(self.coppa_italia_info)

        # Sezione Supercoppa
        supercoppa_label = QLabel("Supercoppa Italiana")
        supercoppa_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2E7D32; margin-top: 20px;")
        cups_layout.addWidget(supercoppa_label)

        self.supercoppa_info = QLabel("In attesa di generazione...")
        cups_layout.addWidget(self.supercoppa_info)

        # Sezione Coppa Italia Serie C
        coppa_c_label = QLabel("Coppa Italia Serie C")
        coppa_c_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2E7D32; margin-top: 20px;")
        cups_layout.addWidget(coppa_c_label)

        self.coppa_c_info = QLabel("In attesa di generazione...")
        cups_layout.addWidget(self.coppa_c_info)

        cups_layout.addStretch()
        self.competitions_tabs.addTab(self.cups_widget, "Coppe")

    def create_matches_panel(self) -> QWidget:
        """Crea panel partite"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titolo
        matches_label = QLabel("Prossime Partite")
        matches_font = QFont("Arial", HEADER_FONT_SIZE, QFont.Weight.Bold)
        matches_label.setFont(matches_font)
        layout.addWidget(matches_label)

        # Filtro squadra
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Squadra:"))

        self.team_filter = QComboBox()
        self.team_filter.addItem("Tutte le squadre")
        self.team_filter.currentTextChanged.connect(self.filter_matches)
        filter_layout.addWidget(self.team_filter)

        layout.addLayout(filter_layout)

        # Tabella partite
        self.matches_table = MatchesTableWidget()
        layout.addWidget(self.matches_table)

        return widget

    def load_season(self, season: Season, competition_manager: CompetitionManager,
                   calendar: MatchCalendar):
        """Carica stagione e competizioni"""
        self.season = season
        self.competition_manager = competition_manager
        self.calendar = calendar

        # Aggiorna interfacce
        self.season_info.update_season_info(season)
        self.update_competitions_display()
        self.update_matches_display()
        self.update_team_filter()

    def update_competitions_display(self):
        """Aggiorna visualizzazione competizioni"""
        if not self.competition_manager:
            return

        # Aggiorna classifiche
        standings = self.competition_manager.get_current_standings()

        if "Serie A" in standings:
            comp = self.competition_manager.get_competition("Serie A")
            if isinstance(comp, LeagueCompetition):
                self.serie_a_standings.load_standings(
                    standings["Serie A"], 0, comp.relegation_spots
                )

        if "Serie B" in standings:
            comp = self.competition_manager.get_competition("Serie B")
            if isinstance(comp, LeagueCompetition):
                self.serie_b_standings.load_standings(
                    standings["Serie B"], comp.promotion_spots, comp.relegation_spots
                )

        # Aggiorna i 3 gironi di Serie C
        for girone_name in ["Girone A", "Girone B", "Girone C"]:
            comp_name = f"Serie C - {girone_name}"
            if comp_name in standings:
                comp = self.competition_manager.get_competition(comp_name)
                if isinstance(comp, LeagueCompetition):
                    self.serie_c_standings[girone_name].load_standings(
                        standings[comp_name], comp.promotion_spots, 0
                    )

        # Aggiorna informazioni coppe
        self.update_cups_display()

    def update_matches_display(self):
        """Aggiorna visualizzazione partite"""
        if not self.calendar:
            return

        # Mostra prossime 10 partite
        next_matches = self.calendar.get_next_matches(limit=10)
        self.matches_table.load_matches(next_matches)

    def update_team_filter(self):
        """Aggiorna filtro squadre"""
        if not self.competition_manager:
            return

        self.team_filter.clear()
        self.team_filter.addItem("Tutte le squadre")

        # Aggiungi tutte le squadre
        all_teams = set()
        for competition in self.competition_manager.get_all_competitions():
            all_teams.update(competition.teams)

        for team in sorted(all_teams):
            self.team_filter.addItem(team)

    def filter_matches(self):
        """Filtra partite per squadra"""
        if not self.calendar:
            return

        selected_team = self.team_filter.currentText()

        if selected_team == "Tutte le squadre":
            matches = self.calendar.get_next_matches(limit=10)
        else:
            matches = self.calendar.get_next_matches(selected_team, limit=10)

        self.matches_table.load_matches(matches)

    def refresh_displays(self):
        """Aggiorna tutte le visualizzazioni"""
        if self.season:
            self.season_info.update_season_info(self.season)
            self.update_competitions_display()
            self.update_matches_display()

    def generate_test_season(self):
        """Genera stagione di test"""
        from ..competitions.season import Season
        from ..competitions.competition import setup_italian_competitions
        from ..competitions.calendar import generate_italian_league_calendar
        from ..data.data_loader import data_loader

        # Crea stagione
        season = Season(2025)

        # Ottieni squadre per livello
        teams_by_level = {
            1: [team["nome"] for team in data_loader.get_serie_a_teams()],
            2: [team["nome"] for team in data_loader.get_serie_b_teams()]
        }

        # Ottieni i gironi di Serie C separatamente
        serie_c_groups = data_loader.get_serie_c_groups()

        # Crea competizioni
        competition_manager = setup_italian_competitions(season, teams_by_level, serie_c_groups)

        # Crea calendario
        calendar = generate_italian_league_calendar(season, teams_by_level)

        # Carica nell'interfaccia
        self.load_season(season, competition_manager, calendar)

        self.generate_test_season_button.setText("Stagione Test Caricata")
        self.generate_test_season_button.setEnabled(False)

    def update_cups_display(self):
        """Aggiorna visualizzazione coppe"""
        if not self.competition_manager:
            return

        # Coppa Italia
        coppa_italia = self.competition_manager.get_competition("Coppa Italia")
        if coppa_italia:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(coppa_italia, CupCompetition):
                stats = coppa_italia.get_competition_stats()
                coppa_text = f"Fase attuale: {stats.get('current_round', 'Non iniziata')}\n"
                coppa_text += f"Squadre rimanenti: {stats.get('teams_remaining', 0)}\n"
                coppa_text += f"Squadre eliminate: {stats.get('teams_eliminated', 0)}"

                if stats.get('winner'):
                    coppa_text += f"\n🏆 VINCITORE: {stats['winner']}"

                self.coppa_italia_info.setText(coppa_text)
            else:
                self.coppa_italia_info.setText("Coppa Italia - Struttura standard")

        # Supercoppa Italiana
        supercoppa = self.competition_manager.get_competition("Supercoppa Italiana")
        if supercoppa:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(supercoppa, CupCompetition):
                stats = supercoppa.get_competition_stats()
                super_text = "Squadre qualificate:\n"

                # Mostra squadre qualificate per semifinali
                semifinali = supercoppa.qualified_teams
                if semifinali:
                    for round_name, teams in semifinali.items():
                        if "Semi" in round_name:
                            super_text += f"  {round_name}: {' vs '.join(teams)}\n"

                if stats.get('winner'):
                    super_text += f"\n🏆 VINCITORE: {stats['winner']}"
                else:
                    super_text += "\nFinale prevista: 22 dicembre 2025 (Riyadh)"

                self.supercoppa_info.setText(super_text)

        # Coppa Italia Serie C
        coppa_c = self.competition_manager.get_competition("Coppa Italia Serie C")
        if coppa_c:
            from ..competitions.cup_competitions import CupCompetition
            if isinstance(coppa_c, CupCompetition):
                stats = coppa_c.get_competition_stats()
                coppa_c_text = f"Squadre partecipanti: {len(coppa_c.teams)}\n"
                coppa_c_text += f"Fase attuale: {stats.get('current_round', 'Primo turno')}\n"
                coppa_c_text += f"Squadre eliminate: {stats.get('teams_eliminated', 0)}"

                if stats.get('winner'):
                    coppa_c_text += f"\n🏆 VINCITORE: {stats['winner']}"

                self.coppa_c_info.setText(coppa_c_text)