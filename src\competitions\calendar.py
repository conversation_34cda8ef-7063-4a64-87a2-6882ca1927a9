"""
Sistema Calendar per gestione calendari partite
"""
import datetime
import random
from typing import List, Dict, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from ..core.utils import logger
from .season import Season

class MatchDay(Enum):
    """Giorni della settimana per le partite"""
    MERCOLEDI = 2  # weekday() value
    SABATO = 5
    DOMENICA = 6

class MatchTime(Enum):
    """Orari delle partite"""
    AFTERNOON = "15:00"      # Sabato/Domenica pomeriggio
    EVENING = "20:45"        # Mercoled<PERSON> sera, alcune domeniche
    LUNCH = "12:30"          # Domenica pranzo
    LATE_AFTERNOON = "18:00" # Sabato sera

@dataclass
class Match:
    """Rappresenta una singola partita"""
    matchday: int
    home_team: str
    away_team: str
    date: datetime.date
    time: str
    competition: str
    venue: str = ""

    # Risultato (None se non giocata)
    home_score: Optional[int] = None
    away_score: Optional[int] = None
    played: bool = False

    # Dettagli aggiuntivi
    attendance: Optional[int] = None
    weather: str = "Buono"

    @property
    def result_string(self) -> str:
        """Restituisce risultato come stringa"""
        if not self.played:
            return "vs"
        return f"{self.home_score}-{self.away_score}"

    @property
    def match_datetime(self) -> datetime.datetime:
        """Combina data e ora in datetime"""
        hour, minute = map(int, self.time.split(':'))
        return datetime.datetime.combine(self.date, datetime.time(hour, minute))

    def __str__(self) -> str:
        return f"G{self.matchday}: {self.home_team} {self.result_string} {self.away_team}"

class CalendarGenerator:
    """Generatore di calendari per campionati"""

    def __init__(self):
        self.match_times = {
            MatchDay.SABATO: [MatchTime.AFTERNOON, MatchTime.LATE_AFTERNOON],
            MatchDay.DOMENICA: [MatchTime.LUNCH, MatchTime.AFTERNOON, MatchTime.EVENING],
            MatchDay.MERCOLEDI: [MatchTime.EVENING]
        }

    def generate_round_robin(self, teams: List[str], double: bool = True) -> List[Tuple[str, str]]:
        """Genera calendario all'italiana (tutti contro tutti)"""
        if len(teams) % 2 != 0:
            teams = teams.copy()
            teams.append("RIPOSO")  # Squadra fittizia per numero dispari

        n = len(teams)
        fixtures = []

        # Algoritmo round-robin standard
        for round_num in range(n - 1):
            round_fixtures = []

            for i in range(n // 2):
                home = teams[i]
                away = teams[n - 1 - i]

                if home != "RIPOSO" and away != "RIPOSO":
                    round_fixtures.append((home, away))

            fixtures.extend(round_fixtures)

            # Ruota le squadre (tranne la prima)
            teams = [teams[0]] + [teams[-1]] + teams[1:-1]

        # Andata e ritorno se richiesto
        if double:
            return_fixtures = [(away, home) for home, away in fixtures]
            fixtures.extend(return_fixtures)

        return fixtures

    def assign_dates_to_fixtures(self, fixtures: List[Tuple[str, str]],
                                season: Season,
                                competition_name: str = "Campionato") -> List[Match]:
        """Assegna date e orari alle partite"""
        matches = []
        current_date = season.dates.league_start
        matchday = 1
        fixture_index = 0

        # Calcola partite per giornata (assume 10 squadre = 5 partite per giornata)
        total_teams = len(set([team for fixture in fixtures for team in fixture]))
        matches_per_matchday = total_teams // 2

        while fixture_index < len(fixtures):
            # Trova prossimo giorno valido per partite
            while not season.is_match_day_possible(current_date):
                current_date += datetime.timedelta(days=1)

            # Determina quante partite per questo giorno
            matches_today = min(matches_per_matchday, len(fixtures) - fixture_index)

            # Crea partite per oggi
            for i in range(matches_today):
                if fixture_index >= len(fixtures):
                    break

                home_team, away_team = fixtures[fixture_index]

                # Scegli orario basato su giorno della settimana
                match_day = MatchDay(current_date.weekday())
                available_times = self.match_times.get(match_day, [MatchTime.AFTERNOON])
                match_time = random.choice(available_times)

                # Crea partita
                match = Match(
                    matchday=matchday,
                    home_team=home_team,
                    away_team=away_team,
                    date=current_date,
                    time=match_time.value,
                    competition=competition_name,
                    venue=f"Stadio {home_team}"
                )

                matches.append(match)
                fixture_index += 1

            # Se abbiamo completato una giornata, passa alla prossima
            if len([m for m in matches if m.matchday == matchday]) >= matches_per_matchday:
                matchday += 1

            # Avanza al prossimo giorno possibile
            current_date += datetime.timedelta(days=1)

            # Evita loop infiniti
            if current_date > season.dates.league_end:
                logger.warning(f"Impossibile completare calendario entro fine stagione")
                break

        logger.info(f"Generato calendario con {len(matches)} partite in {matchday-1} giornate")
        return matches

class MatchCalendar:
    """Classe per gestire il calendario delle partite"""

    def __init__(self, season: Season):
        self.season = season
        self.matches: List[Match] = []
        self.matches_by_team: Dict[str, List[Match]] = {}
        self.matches_by_matchday: Dict[int, List[Match]] = {}

    def add_competition_matches(self, matches: List[Match]):
        """Aggiunge partite di una competizione al calendario"""
        self.matches.extend(matches)
        self._rebuild_indexes()

    def _rebuild_indexes(self):
        """Ricostruisce gli indici per ricerca veloce"""
        self.matches_by_team.clear()
        self.matches_by_matchday.clear()

        for match in self.matches:
            # Indice per squadra
            for team in [match.home_team, match.away_team]:
                if team not in self.matches_by_team:
                    self.matches_by_team[team] = []
                self.matches_by_team[team].append(match)

            # Indice per giornata
            if match.matchday not in self.matches_by_matchday:
                self.matches_by_matchday[match.matchday] = []
            self.matches_by_matchday[match.matchday].append(match)

    def get_matches_for_team(self, team_name: str) -> List[Match]:
        """Restituisce tutte le partite di una squadra"""
        return self.matches_by_team.get(team_name, [])

    def get_matches_for_matchday(self, matchday: int) -> List[Match]:
        """Restituisce partite di una giornata"""
        return self.matches_by_matchday.get(matchday, [])

    def get_matches_for_date(self, date: datetime.date) -> List[Match]:
        """Restituisce partite in una data specifica"""
        return [m for m in self.matches if m.date == date]

    def get_next_matches(self, team_name: Optional[str] = None, limit: int = 5) -> List[Match]:
        """Restituisce prossime partite"""
        if team_name:
            team_matches = self.get_matches_for_team(team_name)
            next_matches = [m for m in team_matches if not m.played and m.date >= self.season.current_date]
        else:
            next_matches = [m for m in self.matches if not m.played and m.date >= self.season.current_date]

        # Ordina per data
        next_matches.sort(key=lambda m: (m.date, m.time))
        return next_matches[:limit]

    def get_recent_matches(self, team_name: Optional[str] = None, limit: int = 5) -> List[Match]:
        """Restituisce partite recenti"""
        if team_name:
            team_matches = self.get_matches_for_team(team_name)
            recent_matches = [m for m in team_matches if m.played and m.date <= self.season.current_date]
        else:
            recent_matches = [m for m in self.matches if m.played and m.date <= self.season.current_date]

        # Ordina per data (più recenti prima)
        recent_matches.sort(key=lambda m: (m.date, m.time), reverse=True)
        return recent_matches[:limit]

    def get_matches_in_date_range(self, start_date: datetime.date,
                                 end_date: datetime.date) -> List[Match]:
        """Restituisce partite in un range di date"""
        return [m for m in self.matches if start_date <= m.date <= end_date]

    def has_team_match_on_date(self, team_name: str, date: datetime.date) -> bool:
        """Verifica se una squadra gioca in una data"""
        team_matches = self.get_matches_for_team(team_name)
        return any(m.date == date for m in team_matches)

    def get_free_dates_for_team(self, team_name: str,
                               start_date: datetime.date,
                               end_date: datetime.date) -> List[datetime.date]:
        """Restituisce date libere per una squadra"""
        free_dates = []
        current = start_date

        while current <= end_date:
            if (self.season.is_match_day_possible(current) and
                not self.has_team_match_on_date(team_name, current)):
                free_dates.append(current)
            current += datetime.timedelta(days=1)

        return free_dates

    def schedule_friendly_match(self, home_team: str, away_team: str,
                               date: datetime.date) -> Optional[Match]:
        """Programma un'amichevole"""
        if not self.season.is_match_day_possible(date):
            return None

        if (self.has_team_match_on_date(home_team, date) or
            self.has_team_match_on_date(away_team, date)):
            return None

        # Trova slot temporale libero
        match_day = MatchDay(date.weekday())
        available_times = self.match_times.get(match_day, [MatchTime.AFTERNOON])
        match_time = random.choice(available_times)

        friendly = Match(
            matchday=0,  # Amichevoli hanno matchday 0
            home_team=home_team,
            away_team=away_team,
            date=date,
            time=match_time.value,
            competition="Amichevole",
            venue=f"Stadio {home_team}"
        )

        self.matches.append(friendly)
        self._rebuild_indexes()

        return friendly

    def get_calendar_summary(self) -> Dict:
        """Restituisce riassunto del calendario"""
        total_matches = len(self.matches)
        played_matches = len([m for m in self.matches if m.played])

        competitions = list(set(m.competition for m in self.matches))

        next_matchday_matches = self.get_matches_for_matchday(self.season.current_matchday + 1)

        return {
            "total_matches": total_matches,
            "played_matches": played_matches,
            "remaining_matches": total_matches - played_matches,
            "competitions": competitions,
            "current_matchday": self.season.current_matchday,
            "next_matchday_matches": len(next_matchday_matches),
            "next_match_date": next_matchday_matches[0].date if next_matchday_matches else None
        }

    def export_calendar_csv(self, filename: str):
        """Esporta calendario in CSV"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Giornata', 'Data', 'Ora', 'Casa', 'Trasferta', 'Competizione', 'Risultato']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for match in sorted(self.matches, key=lambda m: (m.date, m.time)):
                writer.writerow({
                    'Giornata': match.matchday,
                    'Data': match.date.strftime('%d/%m/%Y'),
                    'Ora': match.time,
                    'Casa': match.home_team,
                    'Trasferta': match.away_team,
                    'Competizione': match.competition,
                    'Risultato': match.result_string
                })

# Funzioni utility per la generazione calendario

def create_season_calendar(season: Season, competitions_data: List[Dict]) -> MatchCalendar:
    """Crea calendario completo per una stagione"""
    calendar = MatchCalendar(season)
    generator = CalendarGenerator()

    for comp_data in competitions_data:
        comp_name = comp_data["name"]
        teams = comp_data["teams"]
        double_round = comp_data.get("double_round", True)

        logger.info(f"Generando calendario per {comp_name} ({len(teams)} squadre)")

        # Genera fixtures
        fixtures = generator.generate_round_robin(teams, double=double_round)

        # Assegna date
        matches = generator.assign_dates_to_fixtures(fixtures, season, comp_name)

        # Aggiunge al calendario principale
        calendar.add_competition_matches(matches)

    return calendar

def generate_italian_league_calendar(season: Season, teams_by_level: Dict[int, List[str]]) -> MatchCalendar:
    """Genera calendario per i campionati italiani"""
    competitions = []

    level_names = {1: "Serie A", 2: "Serie B", 3: "Serie C"}

    for level, teams in teams_by_level.items():
        if teams:
            competitions.append({
                "name": level_names.get(level, f"Livello {level}"),
                "teams": teams,
                "double_round": True
            })

    return create_season_calendar(season, competitions)