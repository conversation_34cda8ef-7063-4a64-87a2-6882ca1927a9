"""
Data Loader per caricamento dati dai file JSON
"""
import json
import codecs
from typing import Dict, List, Optional, Any
from pathlib import Path

from ..core.config import *
from ..core.utils import logger, load_json_file

class DataLoader:
    """Classe per il caricamento e gestione dati JSON"""

    def __init__(self):
        self.leagues_data = {}
        self.player_names_data = {}
        self.fifa_codes_data = {}
        self._loaded = False

    def load_all_data(self) -> bool:
        """Carica tutti i dati necessari"""
        try:
            logger.info("Inizio caricamento dati...")

            # Carica nomi giocatori
            if not self._load_player_names():
                logger.error("Errore caricamento nomi giocatori")
                return False

            # Carica codici FIFA
            if not self._load_fifa_codes():
                logger.error("Errore caricamento codici FIFA")
                return False

            # Carica campionati
            if not self._load_all_leagues():
                logger.error("Errore caricamento campionati")
                return False

            self._loaded = True
            logger.info("Caricamento dati completato con successo")
            return True

        except Exception as e:
            logger.error(f"Errore durante caricamento dati: {e}")
            return False

    def _load_player_names(self) -> bool:
        """Carica i nomi dei giocatori dalle 110 nazioni"""
        try:
            with codecs.open(PLAYER_NAMES_FILE, 'r', 'utf-8') as f:
                self.player_names_data = json.load(f)

            logger.info(f"Caricati nomi per {len(self.player_names_data)} nazioni")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento nomi giocatori: {e}")
            return False

    def _load_fifa_codes(self) -> bool:
        """Carica i codici FIFA dei paesi"""
        try:
            if FIFA_CODES_FILE.exists():
                self.fifa_codes_data = load_json_file(FIFA_CODES_FILE)
                logger.info(f"Caricati codici FIFA per {len(self.fifa_codes_data)} paesi")
            return True

        except Exception as e:
            logger.error(f"Errore caricamento codici FIFA: {e}")
            return False

    def _load_all_leagues(self) -> bool:
        """Carica tutti i campionati disponibili"""
        try:
            # Carica campionati italiani
            italy_leagues = self._load_country_leagues("Italia")
            if italy_leagues:
                self.leagues_data["Italia"] = italy_leagues
                logger.info(f"Caricati {len(italy_leagues)} campionati italiani")

            # Carica altri campionati principali per completezza
            main_countries = ["Germania", "Spagna", "Francia", "Inghilterra", "Brasile"]
            for country in main_countries:
                country_leagues = self._load_country_leagues(country)
                if country_leagues:
                    self.leagues_data[country] = country_leagues

            return len(self.leagues_data) > 0

        except Exception as e:
            logger.error(f"Errore caricamento campionati: {e}")
            return False

    def _load_country_leagues(self, country: str) -> Dict:
        """Carica i campionati di una nazione"""
        country_dir = BASE_DIR / country
        leagues = {}

        if not country_dir.exists():
            logger.warning(f"Directory non trovata: {country_dir}")
            return leagues

        try:
            # Cerca tutti i file JSON nella directory del paese
            json_files = list(country_dir.glob("*.json"))

            for json_file in json_files:
                league_data = load_json_file(json_file)
                if league_data:
                    league_name = json_file.stem.replace("_", " ")
                    leagues[league_name] = league_data

            return leagues

        except Exception as e:
            logger.error(f"Errore caricamento campionati {country}: {e}")
            return {}

    def get_italian_leagues(self) -> Dict:
        """Restituisce i campionati italiani"""
        return self.leagues_data.get("Italia", {})

    def get_serie_a_teams(self) -> List[Dict]:
        """Restituisce le squadre di Serie A"""
        italian_leagues = self.get_italian_leagues()
        serie_a = italian_leagues.get("Serie A") or italian_leagues.get("Serie_A")

        if serie_a and "squadre" in serie_a:
            return serie_a["squadre"]
        return []

    def get_serie_b_teams(self) -> List[Dict]:
        """Restituisce le squadre di Serie B"""
        italian_leagues = self.get_italian_leagues()
        serie_b = italian_leagues.get("Serie B") or italian_leagues.get("Serie_B")

        if serie_b and "squadre" in serie_b:
            return serie_b["squadre"]
        return []

    def get_serie_c_teams(self) -> List[Dict]:
        """Restituisce tutte le squadre di Serie C"""
        italian_leagues = self.get_italian_leagues()
        serie_c_teams = []

        # Serie C ha 3 gironi
        for girone in ["A", "B", "C"]:
            girone_key = f"Serie C - Girone {girone}"
            girone_alt_key = f"Serie_C_-_Girone_{girone}"

            girone_data = italian_leagues.get(girone_key) or italian_leagues.get(girone_alt_key)
            if girone_data and "squadre" in girone_data:
                serie_c_teams.extend(girone_data["squadre"])

        return serie_c_teams

    def get_serie_c_groups(self) -> Dict[str, List[str]]:
        """Restituisce i 3 gironi di Serie C separatamente"""
        italian_leagues = self.get_italian_leagues()
        serie_c_groups = {}

        # Serie C ha 3 gironi
        for girone in ["A", "B", "C"]:
            girone_key = f"Serie C - Girone {girone}"
            girone_alt_key = f"Serie_C_-_Girone_{girone}"

            girone_data = italian_leagues.get(girone_key) or italian_leagues.get(girone_alt_key)
            if girone_data and "squadre" in girone_data:
                # Estrai solo i nomi delle squadre
                team_names = [team["nome"] for team in girone_data["squadre"] if "nome" in team]
                serie_c_groups[f"Girone {girone}"] = team_names

        return serie_c_groups

    def get_all_italian_teams(self) -> List[Dict]:
        """Restituisce tutte le squadre italiane"""
        all_teams = []
        all_teams.extend(self.get_serie_a_teams())
        all_teams.extend(self.get_serie_b_teams())
        all_teams.extend(self.get_serie_c_teams())
        return all_teams

    def get_teams_by_level(self, level: int) -> List[Dict]:
        """Restituisce squadre per livello competizione"""
        if level == 1:
            return self.get_serie_a_teams()
        elif level == 2:
            return self.get_serie_b_teams()
        elif level == 3:
            return self.get_serie_c_teams()
        else:
            return []

    def get_team_by_name(self, team_name: str) -> Optional[Dict]:
        """Trova una squadra per nome"""
        all_teams = self.get_all_italian_teams()
        for team in all_teams:
            if team.get("nome", "").lower() == team_name.lower():
                return team
        return None

    def get_available_nations_for_names(self) -> List[str]:
        """Restituisce lista nazioni disponibili per nomi"""
        return list(self.player_names_data.keys())

    def get_names_for_nation(self, nation: str) -> Dict[str, List[str]]:
        """Restituisce nomi e cognomi per una nazione"""
        return self.player_names_data.get(nation, {"first_names": [], "last_names": []})

    def get_league_info(self, country: str, league_name: str) -> Optional[Dict]:
        """Restituisce informazioni su un campionato"""
        country_leagues = self.leagues_data.get(country, {})
        return country_leagues.get(league_name)

    def validate_data_integrity(self) -> bool:
        """Valida l'integrità dei dati caricati"""
        if not self._loaded:
            logger.error("Dati non ancora caricati")
            return False

        # Verifica nomi giocatori
        if not self.player_names_data:
            logger.error("Nomi giocatori non caricati")
            return False

        # Verifica campionati italiani
        italian_teams = self.get_all_italian_teams()
        if len(italian_teams) < 50:  # Dovrebbero essere circa 100 squadre
            logger.warning(f"Solo {len(italian_teams)} squadre italiane caricate")

        # Verifica Serie A
        serie_a_teams = self.get_serie_a_teams()
        if len(serie_a_teams) != 20:
            logger.warning(f"Serie A ha {len(serie_a_teams)} squadre invece di 20")

        logger.info("Validazione dati completata")
        return True

    def get_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche sui dati caricati"""
        stats = {
            "nazioni_nomi": len(self.player_names_data),
            "paesi_campionati": len(self.leagues_data),
            "squadre_italiane": len(self.get_all_italian_teams()),
            "squadre_serie_a": len(self.get_serie_a_teams()),
            "squadre_serie_b": len(self.get_serie_b_teams()),
            "squadre_serie_c": len(self.get_serie_c_teams())
        }

        # Aggiungi dettagli per nazione
        for nation, names_data in self.player_names_data.items():
            first_names = len(names_data.get("first_names", []))
            last_names = len(names_data.get("last_names", []))
            stats[f"nomi_{nation}"] = {"primi": first_names, "cognomi": last_names}

        return stats

    def is_loaded(self) -> bool:
        """Verifica se i dati sono stati caricati"""
        return self._loaded

# Istanza globale del data loader
data_loader = DataLoader()