"""
Interfaccia utente per la gestione squadra
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLabel, QPushButton,
                            QComboBox, QGroupBox, QSplitter, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..core.config import COLOR_PRIMARY, COLOR_TEXT, MAIN_FONT_SIZE, HEADER_FONT_SIZE
from ..core.utils import format_currency
from ..players.player import Player, PlayerPosition
from typing import List, Optional, Dict

class PlayerTableWidget(QTableWidget):
    """Widget tabella giocatori personalizzato"""

    player_selected = pyqtSignal(object)  # Emette Player selezionato

    def __init__(self, parent=None):
        super().__init__(parent)
        self.players: List[Player] = []
        self.setup_table()

    def setup_table(self):
        """Configura la tabella"""
        # Colonne
        headers = ["N°", "Nome", "Pos", "Età", "Naz", "Overall", "Valore", "Contratto"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # Configurazione tabella
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)

        # Dimensioni colonne
        header = self.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nome

        # Connessioni
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def load_players(self, players: List[Player]):
        """Carica giocatori nella tabella"""
        self.players = players
        self.setRowCount(len(players))

        for row, player in enumerate(players):
            # N° maglia
            shirt_item = QTableWidgetItem(str(player.shirt_number or ""))
            shirt_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 0, shirt_item)

            # Nome
            name_item = QTableWidgetItem(player.full_name)
            self.setItem(row, 1, name_item)

            # Posizione
            pos_item = QTableWidgetItem(player.position.value)
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 2, pos_item)

            # Età
            age_item = QTableWidgetItem(str(player.age))
            age_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 3, age_item)

            # Nazionalità
            nat_item = QTableWidgetItem(player.nationality[:3].upper())
            nat_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 4, nat_item)

            # Overall
            overall_item = QTableWidgetItem(str(player.overall_rating))
            overall_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # Colora overall in base al valore
            if player.overall_rating >= 16:
                overall_item.setBackground(Qt.GlobalColor.green)
            elif player.overall_rating >= 13:
                overall_item.setBackground(Qt.GlobalColor.yellow)

            self.setItem(row, 5, overall_item)

            # Valore
            value_item = QTableWidgetItem(format_currency(player.market_value))
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)
            self.setItem(row, 6, value_item)

            # Contratto
            contract_years = (player.contract.data_scadenza.year - 2025)
            contract_item = QTableWidgetItem(f"{contract_years} anni")
            contract_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 7, contract_item)

    def on_selection_changed(self):
        """Gestisce cambio selezione"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.players):
            selected_player = self.players[current_row]
            self.player_selected.emit(selected_player)

class PlayerDetailWidget(QWidget):
    """Widget dettagli giocatore selezionato"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_player: Optional[Player] = None
        self.setup_ui()

    def setup_ui(self):
        """Configura interfaccia dettagli"""
        layout = QVBoxLayout(self)

        # Titolo
        self.title_label = QLabel("Dettagli Giocatore")
        title_font = QFont("Arial", HEADER_FONT_SIZE, QFont.Weight.Bold)
        self.title_label.setFont(title_font)
        layout.addWidget(self.title_label)

        # Info base
        base_group = QGroupBox("Informazioni Base")
        base_layout = QVBoxLayout(base_group)

        self.name_label = QLabel("Seleziona un giocatore")
        self.name_label.setStyleSheet(f"font-size: {MAIN_FONT_SIZE + 2}pt; font-weight: bold;")
        base_layout.addWidget(self.name_label)

        self.info_label = QLabel("")
        self.info_label.setWordWrap(True)
        base_layout.addWidget(self.info_label)

        layout.addWidget(base_group)

        # Attributi
        attr_group = QGroupBox("Attributi")
        attr_layout = QVBoxLayout(attr_group)

        self.attributes_label = QLabel("")
        self.attributes_label.setWordWrap(True)
        attr_layout.addWidget(self.attributes_label)

        layout.addWidget(attr_group)

        # Statistiche stagionali
        stats_group = QGroupBox("Statistiche Stagione")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_label = QLabel("")
        self.stats_label.setWordWrap(True)
        stats_layout.addWidget(self.stats_label)

        layout.addWidget(stats_group)

        layout.addStretch()

    def show_player_details(self, player: Player):
        """Mostra dettagli del giocatore"""
        self.current_player = player

        # Nome
        self.name_label.setText(player.full_name)

        # Info base
        info_text = f"Età: {player.age} anni\n"
        info_text += f"Nazionalità: {player.nationality}\n"
        info_text += f"Posizione: {player.position.value}\n"
        info_text += f"Piede: {player.foot_preference.value}\n"
        info_text += f"Overall: {player.overall_rating} (Pot: {player.potential})\n"
        info_text += f"Valore mercato: {format_currency(player.market_value)}\n"
        info_text += f"Stipendio: {format_currency(player.contract.stipendio_annuale)}/anno"

        self.info_label.setText(info_text)

        # Attributi
        attr_text = f"Tecnica: {player.attributes.tecnica}   "
        attr_text += f"Velocità: {player.attributes.velocita}   "
        attr_text += f"Forza: {player.attributes.forza}\n"
        attr_text += f"Resistenza: {player.attributes.resistenza}   "
        attr_text += f"Tiro: {player.attributes.tiro}   "
        attr_text += f"Passaggio: {player.attributes.passaggio}\n"
        attr_text += f"Dribbling: {player.attributes.dribbling}   "
        attr_text += f"Difesa: {player.attributes.difesa}   "
        attr_text += f"Mentalità: {player.attributes.mentalita}\n"

        if player.position == PlayerPosition.PORTIERE:
            attr_text += f"Portiere: {player.attributes.portiere}"

        self.attributes_label.setText(attr_text)

        # Statistiche
        stats = player.current_season_stats
        stats_text = f"Presenze: {stats.presenze}\n"
        stats_text += f"Minuti: {stats.minuti_giocati}\n"
        stats_text += f"Gol: {stats.gol}   Assist: {stats.assist}\n"
        stats_text += f"Cartellini: {stats.cartellini_gialli} gialli, {stats.cartellini_rossi} rossi\n"

        if stats.presenze > 0:
            stats_text += f"Voto medio: {stats.voto_medio:.1f}"
        else:
            stats_text += "Voto medio: N/A"

        self.stats_label.setText(stats_text)

class SquadUI(QWidget):
    """Interfaccia principale gestione squadra"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.squad: List[Player] = []
        self.setup_ui()

    def setup_ui(self):
        """Configura interfaccia"""
        layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()

        # Titolo
        title_label = QLabel("Gestione Squadra")
        title_font = QFont("Arial", HEADER_FONT_SIZE + 2, QFont.Weight.Bold)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Filtri
        self.position_filter = QComboBox()
        self.position_filter.addItems(["Tutte le posizioni", "P", "Difensori", "Centrocampisti", "Attaccanti"])
        self.position_filter.currentTextChanged.connect(self.filter_players)
        header_layout.addWidget(QLabel("Filtro:"))
        header_layout.addWidget(self.position_filter)

        layout.addLayout(header_layout)

        # Splitter principale
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Tabella giocatori (sinistra)
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Info squadra
        self.squad_info_label = QLabel("Rosa: 0 giocatori")
        self.squad_info_label.setStyleSheet(f"font-weight: bold; color: {COLOR_PRIMARY};")
        left_layout.addWidget(self.squad_info_label)

        # Tabella
        self.players_table = PlayerTableWidget()
        self.players_table.player_selected.connect(self.on_player_selected)
        left_layout.addWidget(self.players_table)

        main_splitter.addWidget(left_widget)

        # Dettagli giocatore (destra)
        self.player_details = PlayerDetailWidget()
        main_splitter.addWidget(self.player_details)

        # Dimensioni splitter
        main_splitter.setSizes([600, 300])

        layout.addWidget(main_splitter)

        # Bottoni azione
        buttons_layout = QHBoxLayout()

        self.contract_button = QPushButton("Rinnova Contratto")
        self.contract_button.setEnabled(False)
        buttons_layout.addWidget(self.contract_button)

        self.transfer_button = QPushButton("Metti sul Mercato")
        self.transfer_button.setEnabled(False)
        buttons_layout.addWidget(self.transfer_button)

        buttons_layout.addStretch()

        self.generate_squad_button = QPushButton("Genera Rosa")
        self.generate_squad_button.clicked.connect(self.generate_test_squad)
        buttons_layout.addWidget(self.generate_squad_button)

        layout.addLayout(buttons_layout)

    def load_squad(self, players: List[Player]):
        """Carica rosa giocatori"""
        self.squad = players
        self.update_display()

    def update_display(self):
        """Aggiorna visualizzazione"""
        # Info squadra
        total_players = len(self.squad)
        total_value = sum(p.market_value for p in self.squad)
        avg_age = sum(p.age for p in self.squad) / len(self.squad) if self.squad else 0

        info_text = f"Rosa: {total_players} giocatori | "
        info_text += f"Valore totale: {format_currency(total_value)} | "
        info_text += f"Età media: {avg_age:.1f} anni"

        self.squad_info_label.setText(info_text)

        # Applica filtro corrente
        self.filter_players()

    def filter_players(self):
        """Filtra giocatori per posizione"""
        filter_text = self.position_filter.currentText()

        if filter_text == "Tutte le posizioni":
            filtered_players = self.squad
        elif filter_text == "P":
            filtered_players = [p for p in self.squad if p.position == PlayerPosition.PORTIERE]
        elif filter_text == "Difensori":
            def_positions = [PlayerPosition.DIFENSORE_CENTRALE, PlayerPosition.DIFENSORE_SINISTRO,
                           PlayerPosition.DIFENSORE_DESTRO]
            filtered_players = [p for p in self.squad if p.position in def_positions]
        elif filter_text == "Centrocampisti":
            mid_positions = [PlayerPosition.CENTROCAMPISTA_CENTRALE, PlayerPosition.CENTROCAMPISTA_SINISTRO,
                           PlayerPosition.CENTROCAMPISTA_DESTRO, PlayerPosition.TREQUARTISTA]
            filtered_players = [p for p in self.squad if p.position in mid_positions]
        elif filter_text == "Attaccanti":
            att_positions = [PlayerPosition.ATTACCANTE_SINISTRO, PlayerPosition.ATTACCANTE_DESTRO,
                           PlayerPosition.CENTRAVANTI]
            filtered_players = [p for p in self.squad if p.position in att_positions]
        else:
            filtered_players = self.squad

        self.players_table.load_players(filtered_players)

    def on_player_selected(self, player: Player):
        """Gestisce selezione giocatore"""
        self.player_details.show_player_details(player)

        # Abilita bottoni
        self.contract_button.setEnabled(True)
        self.transfer_button.setEnabled(True)

    def generate_test_squad(self):
        """Genera una rosa di test"""
        from ..players.player_generator import player_generator

        # Genera squadra Serie B (livello 2)
        test_squad = player_generator.generate_team_squad(team_level=2, squad_size=25)
        self.load_squad(test_squad)